{"version": 3, "file": "MFI.js", "sourceRoot": "", "sources": ["../../src/volume/MFI.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AAEnE,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAC3D,OAAO,mBAAmB,MAAM,8BAA8B,CAAC;AAE/D,MAAM,eAAgB,SAAQ,cAAc;CAM3C;AAED,MAAM,UAAW,SAAQ,SAAS;IAE9B,YAAa,KAAc;QACzB,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,KAAK,GAAS,KAAK,CAAC,IAAI,CAAC;QAC7B,IAAI,IAAI,GAAU,KAAK,CAAC,GAAG,CAAC;QAC5B,IAAI,MAAM,GAAQ,KAAK,CAAC,KAAK,CAAC;QAC9B,IAAI,OAAO,GAAO,KAAK,CAAC,MAAM,CAAC;QAC/B,IAAI,MAAM,GAAQ,KAAK,CAAC,MAAM,CAAC;QAE/B,IAAI,YAAY,GAAI,IAAI,YAAY,CAAC,EAAE,GAAG,EAAG,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAC,CAAC,CAAC;QAEvE,IAAI,YAAY,GAAG,IAAI,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACvE,IAAI,YAAY,GAAG,IAAI,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEvE,EAAE,CAAA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC,CAAE,CAAC,CAAA,CAAC;YAC9G,MAAM,CAAC,oDAAoD,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACzB,IAAI,MAAM,CAAC;YACX,IAAI,IAAI,CAAC;YACT,IAAI,SAAS,CAAC;YACd,IAAI,qBAAqB,CAAC;YAC1B,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,cAAc,CAAC;YACnB,IAAI,qBAAqB,CAAC;YAC1B,IAAI,iBAAiB,CAAA;YACrB,IAAI,GAAG,KAAK,CAAC;YACb,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa;YACrC,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,IAAI,EACX,CAAC;gBACC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;gBACxC,IAAI,aAAa,GAAG,CAAC,CAAC;gBACtB,IAAI,aAAa,GAAG,CAAC,CAAC;gBACtB,iBAAiB,GAAG,YAAY,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAC,CAAC,CAAC;gBAChE,YAAY,GAAG,iBAAiB,GAAG,MAAM,CAAC;gBAC1C,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,aAAa,GAAC,YAAY,CAAC,CAAC,CAAC,aAAa,GAAG,YAAY,CAAC;gBAC9E,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;gBAChC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;gBAChC,qBAAqB,GAAG,YAAY,CAAC,SAAS,CAAC;gBAC/C,qBAAqB,GAAG,YAAY,CAAC,SAAS,CAAC;gBAC/C,EAAE,CAAA,CAAC,CAAC,YAAY,CAAC,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC9E,cAAc,GAAG,qBAAqB,GAAG,qBAAqB,CAAC;oBAC/D,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAE,CAAC,GAAG,cAAc,CAAC,CAAC;gBAC/C,CAAC;gBACD,SAAS,GAAG,KAAK,CAAC;gBAClB,IAAI,GAAG,MAAM,MAAM,CAAA;YACrB,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;YAChC,IAAI,SAAS,GAAG;gBACd,IAAI,EAAM,QAAQ;gBAClB,GAAG,EAAO,IAAI,CAAC,KAAK,CAAC;gBACrB,KAAK,EAAK,MAAM,CAAC,KAAK,CAAC;gBACvB,MAAM,EAAI,OAAO,CAAC,KAAK,CAAC;aACzB,CAAA;YACD,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5C,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAA,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC;QACH,CAAC,CAAC,CAAC;IACP,CAAC;IAAA,CAAC;IAIF,SAAS,CAAC,KAAgB;QACvB,IAAI,MAAM,GAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzC,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAA,CAAC;YAC3B,MAAM,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC;IAAA,CAAC;;AAPK,aAAS,GAAG,GAAG,CAAC;AAUzB,MAAM,cAAc,KAAc;IAC5B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC;AAAA,CAAC"}