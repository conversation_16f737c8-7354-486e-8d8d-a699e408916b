#!/usr/bin/env node

// تطبيق محلل البتكوين الاحترافي - تشغيل سطر الأوامر
const { analyzeMarket } = require('./lib/signals');

console.log('🚀 محلل البتكوين الاحترافي');
console.log('==========================');
console.log('تطبيق تحليل فني شامل للبتكوين');
console.log('==========================\n');

// معالج الإشارات للخروج النظيف
process.on('SIGINT', () => {
    console.log('\n\n👋 تم إيقاف التطبيق بواسطة المستخدم');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n\n👋 تم إنهاء التطبيق');
    process.exit(0);
});

async function runAnalysis() {
    try {
        console.log('📡 الاتصال بمصادر البيانات...');
        console.log('⏳ جاري جلب البيانات وتحليلها...\n');
        
        const startTime = Date.now();
        
        // تشغيل التحليل الكامل
        const analysis = await analyzeMarket();
        
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log(`✅ تم إكمال التحليل في ${duration.toFixed(2)} ثانية\n`);
        
        // عرض النتائج المختصرة
        displaySummary(analysis);
        
        // عرض التوصية النهائية
        displayRecommendation(analysis.recommendation);
        
        console.log('\n🔄 لإعادة التحليل، شغل الأمر مرة أخرى');
        console.log('💻 لتشغيل الواجهة الرسومية: npm start');
        
    } catch (error) {
        console.error('\n❌ خطأ في التحليل:', error.message);
        console.log('\n🔧 نصائح لحل المشكلة:');
        console.log('1. تحقق من اتصال الإنترنت');
        console.log('2. تأكد من عدم حجب APIs من قبل Firewall');
        console.log('3. أعد المحاولة بعد دقيقة');
        console.log('4. جرب العرض التوضيحي: node demo.js');
        
        process.exit(1);
    }
}

function displaySummary(analysis) {
    console.log('📊 === ملخص التحليل ===');
    console.log('======================\n');
    
    // معلومات السوق الأساسية
    if (analysis.marketData) {
        const market = analysis.marketData;
        const changeColor = market.change24h >= 0 ? '🟢' : '🔴';
        const changeSign = market.change24h >= 0 ? '+' : '';
        
        console.log('💰 السوق:');
        console.log(`   ${changeColor} السعر: $${market.currentPrice.toLocaleString()}`);
        console.log(`   📈 التغيير (24ساعة): ${changeSign}${market.change24h.toFixed(2)}%`);
        console.log(`   📊 الحجم: ${formatVolume(market.volume24h)}\n`);
    }
    
    // المؤشرات الرئيسية
    if (analysis.indicators) {
        const ind = analysis.indicators;
        console.log('📈 المؤشرات الرئيسية:');
        
        if (ind.currentRSI !== null) {
            const rsiStatus = getRSIStatus(ind.currentRSI);
            console.log(`   📊 RSI: ${ind.currentRSI.toFixed(1)} ${rsiStatus.emoji} ${rsiStatus.text}`);
        }
        
        if (ind.currentMACD) {
            const macdStatus = ind.currentMACD.histogram > 0 ? '🟢 صعودي' : '🔴 هبوطي';
            console.log(`   📈 MACD: ${macdStatus}`);
        }
        
        if (ind.currentEMA50 && ind.currentEMA200) {
            const emaStatus = ind.currentEMA50 > ind.currentEMA200 ? '🟢 صعودي' : '🔴 هبوطي';
            console.log(`   📊 EMA: ${emaStatus}`);
        }
        
        if (ind.currentBB) {
            const bbStatus = getBBStatus(ind.currentBB.position);
            console.log(`   📊 Bollinger: ${bbStatus.emoji} ${bbStatus.text}`);
        }
        
        console.log();
    }
    
    // التوقعات
    if (analysis.forecast && analysis.forecast.combined) {
        const forecast = analysis.forecast.combined;
        const direction = forecast.change24h >= 0 ? '📈' : '📉';
        const changeSign = forecast.change24h >= 0 ? '+' : '';
        
        console.log('🔮 التوقعات:');
        console.log(`   ${direction} 24 ساعة: $${forecast.next24h.toLocaleString()} (${changeSign}${forecast.change24h.toFixed(2)}%)`);
        console.log(`   ${direction} 48 ساعة: $${forecast.next48h.toLocaleString()} (${changeSign}${forecast.change48h.toFixed(2)}%)`);
        console.log(`   🎯 الثقة: ${(forecast.confidence * 100).toFixed(0)}%\n`);
    }
}

function displayRecommendation(recommendation) {
    if (!recommendation) return;
    
    console.log('🎯 === التوصية النهائية ===');
    console.log('==========================\n');
    
    // القرار الرئيسي
    const decisionEmoji = getDecisionEmoji(recommendation.decision);
    console.log(`${decisionEmoji} القرار: ${recommendation.decision}`);
    console.log(`🎯 الثقة: ${recommendation.confidence}%`);
    console.log(`⚠️  المخاطرة: ${recommendation.riskLevel}\n`);
    
    // الأسعار المستهدفة
    if (recommendation.targetPrice24h) {
        console.log('🎯 الأسعار المستهدفة:');
        console.log(`   📅 24 ساعة: $${recommendation.targetPrice24h.toLocaleString()}`);
        if (recommendation.targetPrice48h) {
            console.log(`   📅 48 ساعة: $${recommendation.targetPrice48h.toLocaleString()}`);
        }
        console.log();
    }
    
    // الأسباب
    if (recommendation.reasons && recommendation.reasons.length > 0) {
        console.log('💡 الأسباب الرئيسية:');
        recommendation.reasons.forEach((reason, index) => {
            console.log(`   ${index + 1}. ${reason}`);
        });
        console.log();
    }
    
    // الملخص
    if (recommendation.summary) {
        console.log('📝 الملخص:');
        console.log(`   ${recommendation.summary}\n`);
    }
    
    // تحذير
    console.log('⚠️  تنويه مهم:');
    console.log('   هذا التحليل للأغراض التعليمية والبحثية فقط');
    console.log('   لا يُعتبر نصيحة استثمارية أو مالية');
    console.log('   استشر خبير مالي مؤهل قبل اتخاذ قرارات استثمارية');
    console.log('   الاستثمار في العملات المشفرة ينطوي على مخاطر عالية');
}

// دوال مساعدة
function formatVolume(volume) {
    if (volume >= 1e9) return `${(volume / 1e9).toFixed(1)}B`;
    if (volume >= 1e6) return `${(volume / 1e6).toFixed(1)}M`;
    if (volume >= 1e3) return `${(volume / 1e3).toFixed(1)}K`;
    return volume.toFixed(0);
}

function getRSIStatus(rsi) {
    if (rsi > 70) return { emoji: '🔴', text: 'ذروة شراء' };
    if (rsi < 30) return { emoji: '🟢', text: 'ذروة بيع' };
    if (rsi > 50) return { emoji: '🟡', text: 'إيجابي' };
    return { emoji: '🟡', text: 'سلبي' };
}

function getBBStatus(position) {
    switch (position) {
        case 'above_upper': return { emoji: '🔴', text: 'فوق النطاق العلوي' };
        case 'below_lower': return { emoji: '🟢', text: 'تحت النطاق السفلي' };
        case 'near_upper': return { emoji: '🟡', text: 'قريب من العلوي' };
        case 'near_lower': return { emoji: '🟡', text: 'قريب من السفلي' };
        default: return { emoji: '⚪', text: 'في المنتصف' };
    }
}

function getDecisionEmoji(decision) {
    switch (decision) {
        case 'شراء': return '🟢 💰';
        case 'بيع': return '🔴 💸';
        default: return '🟡 ⏳';
    }
}

// معلومات الاستخدام
function showUsage() {
    console.log('الاستخدام:');
    console.log('  node run-analysis.js    - تشغيل التحليل');
    console.log('  npm start              - تشغيل الواجهة الرسومية');
    console.log('  node demo.js           - عرض توضيحي');
    console.log('  node test/test.js      - اختبار النظام');
}

// التحقق من المعاملات
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showUsage();
    process.exit(0);
}

// تشغيل التحليل
runAnalysis();
