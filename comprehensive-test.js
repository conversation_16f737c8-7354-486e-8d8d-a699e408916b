// اختبار شامل لجميع مكونات التطبيق
console.log('🧪 بدء الاختبار الشامل لمحلل البتكوين...');

async function runComprehensiveTest() {
    const results = {
        libraries: {},
        indicators: {},
        apis: {},
        analysis: {},
        performance: {}
    };

    try {
        // 1. اختبار تحميل المكتبات
        console.log('\n📚 1. اختبار تحميل المكتبات...');
        
        try {
            const signals = require('./lib/signals');
            results.libraries.signals = '✅ نجح';
            console.log('✅ lib/signals تم تحميله');
        } catch (error) {
            results.libraries.signals = `❌ فشل: ${error.message}`;
            console.log('❌ خطأ في lib/signals:', error.message);
        }

        try {
            const indicators = require('./lib/indicators');
            results.libraries.indicators = '✅ نجح';
            console.log('✅ lib/indicators تم تحميله');
        } catch (error) {
            results.libraries.indicators = `❌ فشل: ${error.message}`;
            console.log('❌ خطأ في lib/indicators:', error.message);
        }

        try {
            const fetcher = require('./lib/fetcher');
            results.libraries.fetcher = '✅ نجح';
            console.log('✅ lib/fetcher تم تحميله');
        } catch (error) {
            results.libraries.fetcher = `❌ فشل: ${error.message}`;
            console.log('❌ خطأ في lib/fetcher:', error.message);
        }

        try {
            const patterns = require('./lib/patterns');
            results.libraries.patterns = '✅ نجح';
            console.log('✅ lib/patterns تم تحميله');
        } catch (error) {
            results.libraries.patterns = `❌ فشل: ${error.message}`;
            console.log('❌ خطأ في lib/patterns:', error.message);
        }

        try {
            const forecast = require('./lib/forecast');
            results.libraries.forecast = '✅ نجح';
            console.log('✅ lib/forecast تم تحميله');
        } catch (error) {
            results.libraries.forecast = `❌ فشل: ${error.message}`;
            console.log('❌ خطأ في lib/forecast:', error.message);
        }

        // 2. اختبار المؤشرات الفنية
        console.log('\n📊 2. اختبار المؤشرات الفنية...');
        
        try {
            const { RSI, MACD, EMA, BollingerBands, ATR, ADX, Stochastic, OBV } = require('technicalindicators');
            
            // بيانات اختبار
            const testData = [100, 102, 101, 103, 105, 104, 106, 108, 107, 109, 111, 110, 112, 114, 113, 115];
            
            // اختبار RSI
            try {
                const rsi = RSI.calculate({period: 14, values: testData});
                results.indicators.RSI = rsi.length > 0 ? '✅ نجح' : '❌ لا توجد نتائج';
                console.log('✅ RSI يعمل، النتائج:', rsi.length);
            } catch (error) {
                results.indicators.RSI = `❌ فشل: ${error.message}`;
                console.log('❌ خطأ في RSI:', error.message);
            }

            // اختبار MACD
            try {
                const macd = MACD.calculate({
                    values: testData,
                    fastPeriod: 12,
                    slowPeriod: 26,
                    signalPeriod: 9,
                    SimpleMAOscillator: false,
                    SimpleMASignal: false
                });
                results.indicators.MACD = macd.length > 0 ? '✅ نجح' : '❌ لا توجد نتائج';
                console.log('✅ MACD يعمل، النتائج:', macd.length);
            } catch (error) {
                results.indicators.MACD = `❌ فشل: ${error.message}`;
                console.log('❌ خطأ في MACD:', error.message);
            }

            // اختبار EMA
            try {
                const ema = EMA.calculate({period: 10, values: testData});
                results.indicators.EMA = ema.length > 0 ? '✅ نجح' : '❌ لا توجد نتائج';
                console.log('✅ EMA يعمل، النتائج:', ema.length);
            } catch (error) {
                results.indicators.EMA = `❌ فشل: ${error.message}`;
                console.log('❌ خطأ في EMA:', error.message);
            }

            // اختبار Bollinger Bands
            try {
                const bb = BollingerBands.calculate({period: 10, values: testData, stdDev: 2});
                results.indicators.BollingerBands = bb.length > 0 ? '✅ نجح' : '❌ لا توجد نتائج';
                console.log('✅ Bollinger Bands يعمل، النتائج:', bb.length);
            } catch (error) {
                results.indicators.BollingerBands = `❌ فشل: ${error.message}`;
                console.log('❌ خطأ في Bollinger Bands:', error.message);
            }

        } catch (error) {
            console.log('❌ خطأ في تحميل technicalindicators:', error.message);
        }

        // 3. اختبار APIs
        console.log('\n🌐 3. اختبار APIs...');
        
        const axios = require('axios');
        
        // اختبار CoinGecko
        try {
            const response = await axios.get('https://api.coingecko.com/api/v3/ping', { timeout: 10000 });
            results.apis.coingecko = '✅ متصل';
            console.log('✅ CoinGecko API متصل');
        } catch (error) {
            results.apis.coingecko = `❌ فشل: ${error.message}`;
            console.log('❌ خطأ في CoinGecko:', error.message);
        }

        // اختبار Binance
        try {
            const response = await axios.get('https://api.binance.com/api/v3/ping', { timeout: 10000 });
            results.apis.binance = '✅ متصل';
            console.log('✅ Binance API متصل');
        } catch (error) {
            results.apis.binance = `❌ فشل: ${error.message}`;
            console.log('❌ خطأ في Binance:', error.message);
        }

        // 4. اختبار التحليل الكامل
        console.log('\n📈 4. اختبار التحليل الكامل...');
        
        try {
            const startTime = Date.now();
            const { analyzeMarket } = require('./lib/signals');
            const analysis = await analyzeMarket();
            const endTime = Date.now();
            
            results.analysis.duration = `${endTime - startTime}ms`;
            results.analysis.status = '✅ نجح';
            results.analysis.hasRecommendation = analysis.recommendation ? '✅ نعم' : '❌ لا';
            results.analysis.hasIndicators = analysis.indicators ? '✅ نعم' : '❌ لا';
            results.analysis.hasPatterns = analysis.patterns ? '✅ نعم' : '❌ لا';
            results.analysis.hasForecast = analysis.forecast ? '✅ نعم' : '❌ لا';
            
            console.log('✅ التحليل الكامل نجح في', results.analysis.duration);
            console.log('📊 التوصية:', analysis.recommendation?.decision || 'غير متوفر');
            console.log('💰 السعر المتوقع 24h:', analysis.recommendation?.targetPrice24h || 'غير متوفر');
            
        } catch (error) {
            results.analysis.status = `❌ فشل: ${error.message}`;
            console.log('❌ خطأ في التحليل الكامل:', error.message);
        }

        // 5. اختبار الأداء
        console.log('\n⚡ 5. اختبار الأداء...');
        
        const memoryUsage = process.memoryUsage();
        results.performance.memoryUsed = `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`;
        results.performance.memoryTotal = `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`;
        
        console.log('💾 استهلاك الذاكرة:', results.performance.memoryUsed);
        console.log('💾 إجمالي الذاكرة:', results.performance.memoryTotal);

        // طباعة التقرير النهائي
        console.log('\n' + '='.repeat(60));
        console.log('📋 تقرير الاختبار الشامل');
        console.log('='.repeat(60));
        
        console.log('\n📚 المكتبات:');
        Object.entries(results.libraries).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });
        
        console.log('\n📊 المؤشرات الفنية:');
        Object.entries(results.indicators).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });
        
        console.log('\n🌐 APIs:');
        Object.entries(results.apis).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });
        
        console.log('\n📈 التحليل:');
        Object.entries(results.analysis).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });
        
        console.log('\n⚡ الأداء:');
        Object.entries(results.performance).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });

        // تحديد النتيجة الإجمالية
        const allLibrariesOk = Object.values(results.libraries).every(v => v.includes('✅'));
        const mostIndicatorsOk = Object.values(results.indicators).filter(v => v.includes('✅')).length >= 3;
        const analysisOk = results.analysis.status?.includes('✅');
        
        console.log('\n🏆 النتيجة الإجمالية:');
        if (allLibrariesOk && mostIndicatorsOk && analysisOk) {
            console.log('✅ جميع الاختبارات نجحت! التطبيق جاهز للبناء.');
            return true;
        } else {
            console.log('⚠️ بعض الاختبارات فشلت. يحتاج إصلاح قبل البناء.');
            return false;
        }

    } catch (error) {
        console.error('💥 خطأ في الاختبار الشامل:', error);
        return false;
    }
}

// تشغيل الاختبار
runComprehensiveTest().then(success => {
    console.log('\n🏁 انتهى الاختبار الشامل');
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('💥 فشل الاختبار:', error);
    process.exit(1);
});
