// اختبار نهائي شامل للتطبيق المبني
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🔍 بدء التحقق النهائي من التطبيق...');

async function runFinalVerification() {
    const results = {
        files: {},
        sizes: {},
        functionality: {},
        performance: {}
    };

    try {
        // 1. فحص وجود الملفات
        console.log('\n📁 1. فحص وجود الملفات...');
        
        const requiredFiles = [
            'dist/Bitcoin Analyzer Pro-1.0.0-x64.exe',
            'dist/Bitcoin Analyzer Pro-1.0.0-Portable.exe',
            'dist/win-unpacked/Bitcoin Analyzer Pro.exe',
            'Bitcoin-Analyzer-Pro-Final-v1.0.0/Bitcoin Analyzer Pro-1.0.0-x64.exe',
            'Bitcoin-Analyzer-Pro-Final-v1.0.0/Bitcoin Analyzer Pro-1.0.0-Portable.exe',
            'Bitcoin-Analyzer-Pro-Final-v1.0.0/اقرأني أولاً.txt',
            'Bitcoin-Analyzer-Pro-Final-v1.0.0/تعليمات التشغيل التفصيلية.txt',
            'Bitcoin-Analyzer-Pro-Final-v1.0.0/معلومات النظام.txt',
            'Bitcoin-Analyzer-Pro-Final-v1.0.0/README.md'
        ];

        for (const file of requiredFiles) {
            if (fs.existsSync(file)) {
                const stats = fs.statSync(file);
                const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
                results.files[file] = '✅ موجود';
                results.sizes[file] = `${sizeInMB} MB`;
                console.log(`✅ ${file} (${sizeInMB} MB)`);
            } else {
                results.files[file] = '❌ مفقود';
                console.log(`❌ ${file} - مفقود`);
            }
        }

        // 2. فحص أحجام الملفات
        console.log('\n📊 2. فحص أحجام الملفات...');
        
        const installerPath = 'dist/Bitcoin Analyzer Pro-1.0.0-x64.exe';
        const portablePath = 'dist/Bitcoin Analyzer Pro-1.0.0-Portable.exe';
        
        if (fs.existsSync(installerPath)) {
            const installerSize = fs.statSync(installerPath).size / (1024 * 1024);
            console.log(`📦 Installer: ${installerSize.toFixed(2)} MB`);
            results.sizes.installer = `${installerSize.toFixed(2)} MB`;
            
            if (installerSize > 50 && installerSize < 150) {
                results.functionality.installerSize = '✅ حجم مناسب';
            } else {
                results.functionality.installerSize = '⚠️ حجم غير عادي';
            }
        }
        
        if (fs.existsSync(portablePath)) {
            const portableSize = fs.statSync(portablePath).size / (1024 * 1024);
            console.log(`💼 Portable: ${portableSize.toFixed(2)} MB`);
            results.sizes.portable = `${portableSize.toFixed(2)} MB`;
            
            if (portableSize > 50 && portableSize < 150) {
                results.functionality.portableSize = '✅ حجم مناسب';
            } else {
                results.functionality.portableSize = '⚠️ حجم غير عادي';
            }
        }

        // 3. اختبار تشغيل سريع للنسخة المفكوكة
        console.log('\n🚀 3. اختبار تشغيل سريع...');
        
        const unpackedPath = 'dist/win-unpacked/Bitcoin Analyzer Pro.exe';
        if (fs.existsSync(unpackedPath)) {
            console.log('🔄 محاولة تشغيل النسخة المفكوكة...');
            
            try {
                const testProcess = spawn(unpackedPath, [], {
                    detached: true,
                    stdio: 'ignore'
                });
                
                // انتظار قصير للتأكد من بدء التشغيل
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // إنهاء العملية
                try {
                    process.kill(testProcess.pid);
                    results.functionality.quickLaunch = '✅ يعمل';
                    console.log('✅ التطبيق يعمل بشكل صحيح');
                } catch (error) {
                    results.functionality.quickLaunch = '⚠️ تم التشغيل لكن لم يتم إنهاؤه';
                    console.log('⚠️ التطبيق يعمل لكن لم يتم إنهاؤه تلقائياً');
                }
                
            } catch (error) {
                results.functionality.quickLaunch = '❌ فشل التشغيل';
                console.log('❌ فشل في تشغيل التطبيق:', error.message);
            }
        } else {
            results.functionality.quickLaunch = '❌ الملف غير موجود';
            console.log('❌ ملف التطبيق المفكوك غير موجود');
        }

        // 4. فحص محتوى ملفات التعليمات
        console.log('\n📄 4. فحص محتوى ملفات التعليمات...');
        
        const instructionFiles = [
            'Bitcoin-Analyzer-Pro-Final-v1.0.0/اقرأني أولاً.txt',
            'Bitcoin-Analyzer-Pro-Final-v1.0.0/تعليمات التشغيل التفصيلية.txt',
            'Bitcoin-Analyzer-Pro-Final-v1.0.0/معلومات النظام.txt'
        ];

        for (const file of instructionFiles) {
            if (fs.existsSync(file)) {
                const content = fs.readFileSync(file, 'utf8');
                const wordCount = content.split(/\s+/).length;
                const hasArabic = /[\u0600-\u06FF]/.test(content);
                
                results.functionality[`${path.basename(file)}_content`] = 
                    `✅ ${wordCount} كلمة، عربي: ${hasArabic ? 'نعم' : 'لا'}`;
                
                console.log(`✅ ${path.basename(file)}: ${wordCount} كلمة، عربي: ${hasArabic ? 'نعم' : 'لا'}`);
            }
        }

        // 5. فحص مكتبة التحليل
        console.log('\n🧪 5. فحص مكتبة التحليل...');
        
        try {
            const { analyzeMarket } = require('./lib/signals');
            console.log('✅ مكتبة التحليل تم تحميلها');
            
            // اختبار سريع للتحليل
            const startTime = Date.now();
            const analysis = await analyzeMarket();
            const endTime = Date.now();
            
            const duration = endTime - startTime;
            results.performance.analysisTime = `${duration}ms`;
            results.functionality.analysisWorks = '✅ يعمل';
            
            console.log(`✅ التحليل يعمل في ${duration}ms`);
            console.log(`📊 التوصية: ${analysis.recommendation?.decision || 'غير متوفر'}`);
            
        } catch (error) {
            results.functionality.analysisWorks = `❌ فشل: ${error.message}`;
            console.log('❌ خطأ في مكتبة التحليل:', error.message);
        }

        // 6. فحص الذاكرة والأداء
        console.log('\n⚡ 6. فحص الأداء...');
        
        const memoryUsage = process.memoryUsage();
        results.performance.memoryUsed = `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`;
        results.performance.memoryTotal = `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`;
        
        console.log(`💾 استهلاك الذاكرة: ${results.performance.memoryUsed}`);
        console.log(`💾 إجمالي الذاكرة: ${results.performance.memoryTotal}`);

        // طباعة التقرير النهائي
        console.log('\n' + '='.repeat(60));
        console.log('📋 تقرير التحقق النهائي');
        console.log('='.repeat(60));
        
        console.log('\n📁 الملفات:');
        Object.entries(results.files).forEach(([key, value]) => {
            console.log(`  ${path.basename(key)}: ${value}`);
        });
        
        console.log('\n📊 الأحجام:');
        Object.entries(results.sizes).forEach(([key, value]) => {
            if (key.includes('.exe')) {
                console.log(`  ${path.basename(key)}: ${value}`);
            }
        });
        
        console.log('\n🔧 الوظائف:');
        Object.entries(results.functionality).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });
        
        console.log('\n⚡ الأداء:');
        Object.entries(results.performance).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
        });

        // تحديد النتيجة الإجمالية
        const allFilesExist = Object.values(results.files).every(v => v.includes('✅'));
        const analysisWorks = results.functionality.analysisWorks?.includes('✅');
        const canLaunch = results.functionality.quickLaunch?.includes('✅') || 
                         results.functionality.quickLaunch?.includes('⚠️');
        
        console.log('\n🏆 النتيجة النهائية:');
        if (allFilesExist && analysisWorks && canLaunch) {
            console.log('✅ جميع الاختبارات نجحت! التطبيق جاهز للتوزيع.');
            console.log('🚀 يمكن توزيع الحزمة النهائية بثقة.');
            return true;
        } else {
            console.log('⚠️ بعض الاختبارات فشلت أو تحتاج مراجعة.');
            console.log('🔧 راجع النتائج أعلاه قبل التوزيع.');
            return false;
        }

    } catch (error) {
        console.error('💥 خطأ في التحقق النهائي:', error);
        return false;
    }
}

// تشغيل التحقق
runFinalVerification().then(success => {
    console.log('\n🏁 انتهى التحقق النهائي');
    
    if (success) {
        console.log('\n🎉 التطبيق جاهز للتوزيع!');
        console.log('📦 الحزمة النهائية: Bitcoin-Analyzer-Pro-Final-v1.0.0/');
        console.log('💼 الملفات التنفيذية جاهزة للاستخدام');
        console.log('📄 جميع ملفات التعليمات متوفرة');
    } else {
        console.log('\n⚠️ يحتاج مراجعة قبل التوزيع');
    }
    
    process.exit(success ? 0 : 1);
}).catch(error => {
    console.error('💥 فشل التحقق:', error);
    process.exit(1);
});
