{"name": "technicalindicators", "version": "3.1.0", "description": "Techincal Indicators written in javascript", "main": "dist/index.js", "module": "lib/index.js", "types": "./declarations/index.d.ts", "jsnext:main": "lib/index.js", "repository": {"type": "git", "url": "git+https://github.com/anandanand84/technicalindicators.git"}, "author": "<EMAIL>", "license": "MIT", "sideEffects": false, "homepage": "https://github.com/anandanand84/technicalindicators#readme", "devDependencies": {"@types/chai": "^3.0.0", "@types/mocha": "^2.0.0", "@types/node": "^6.0.31", "babel-cli": "^6.8.0", "babel-core": "^6.8.0", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-es2015": "^6.24.1", "babel-preset-es2015-rollup": "^3.0.0", "babel-register": "^6.8.0", "babel-runtime": "^6.23.0", "chai": "^3.0.0", "draw-candlestick": "2.0.3", "dts-bundle": "^0.7.3", "gulp": "^3.9.1", "gulp-mocha": "^2.2.0", "gulp-util": "^3.0.7", "inquirer": "^5.1.0", "koa": "^2.2.0", "koa-static": "^3.0.0", "lit-html": "^0.9.0", "mocha": "^2.4.5", "monaco-editor": "^0.8.3", "rimraf": "^2.0.0", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-babel-minify": "^3.1.2", "rollup-plugin-commonjs": "^8.3.0", "rollup-plugin-ignore": "^1.0.3", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-typescript2": "^0.18.0", "rollup-plugin-uglify": "^1.0.2", "rollup-watch": "^3.2.2", "ts-node": "^1.0.0", "tslint": "^4.0.0", "typescript": "^2.9.2"}, "dependencies": {"@types/node": "^6.0.96"}}