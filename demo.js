// ملف تجريبي لعرض وظائف التطبيق بدون التبعيات الخارجية

console.log('🚀 مرحباً بك في محلل البتكوين الاحترافي');
console.log('=======================================\n');

// محاكاة بيانات البتكوين
function generateMockData() {
    const data = [];
    const basePrice = 50000;
    const now = new Date();
    
    console.log('📊 إنشاء بيانات تجريبية...');
    
    for (let i = 0; i < 72; i++) {
        const timestamp = new Date(now.getTime() - (i * 60 * 60 * 1000));
        const volatility = 0.02; // 2% تقلبات
        const trend = Math.sin(i * 0.1) * 0.01; // اتجاه تدريجي
        const noise = (Math.random() - 0.5) * volatility;
        
        const priceChange = trend + noise;
        const price = basePrice * (1 + priceChange);
        
        data.unshift({
            timestamp,
            open: price * (1 + (Math.random() - 0.5) * 0.005),
            high: price * (1 + Math.random() * 0.01),
            low: price * (1 - Math.random() * 0.01),
            close: price,
            volume: 1000 + Math.random() * 500
        });
    }
    
    console.log(`✅ تم إنشاء ${data.length} شمعة تجريبية\n`);
    return data;
}

// حساب مؤشرات بسيطة
function calculateSimpleIndicators(data) {
    console.log('🔢 حساب المؤشرات الفنية...');
    
    const closes = data.map(d => d.close);
    const highs = data.map(d => d.high);
    const lows = data.map(d => d.low);
    const volumes = data.map(d => d.volume);
    
    // RSI مبسط
    function calculateRSI(prices, period = 14) {
        if (prices.length < period + 1) return null;
        
        let gains = 0;
        let losses = 0;
        
        for (let i = 1; i <= period; i++) {
            const change = prices[prices.length - i] - prices[prices.length - i - 1];
            if (change > 0) gains += change;
            else losses -= change;
        }
        
        const avgGain = gains / period;
        const avgLoss = losses / period;
        const rs = avgGain / avgLoss;
        return 100 - (100 / (1 + rs));
    }
    
    // متوسط متحرك بسيط
    function calculateSMA(prices, period) {
        if (prices.length < period) return null;
        const sum = prices.slice(-period).reduce((a, b) => a + b, 0);
        return sum / period;
    }
    
    // متوسط متحرك أسي
    function calculateEMA(prices, period) {
        if (prices.length < period) return null;
        const multiplier = 2 / (period + 1);
        let ema = prices.slice(0, period).reduce((a, b) => a + b, 0) / period;
        
        for (let i = period; i < prices.length; i++) {
            ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
        }
        return ema;
    }
    
    const indicators = {
        rsi: calculateRSI(closes),
        sma20: calculateSMA(closes, 20),
        sma50: calculateSMA(closes, 50),
        ema20: calculateEMA(closes, 20),
        ema50: calculateEMA(closes, 50),
        currentPrice: closes[closes.length - 1],
        volume24h: volumes.slice(-24).reduce((a, b) => a + b, 0),
        high24h: Math.max(...highs.slice(-24)),
        low24h: Math.min(...lows.slice(-24))
    };
    
    console.log('✅ تم حساب المؤشرات الأساسية\n');
    return indicators;
}

// تحليل الإشارات
function analyzeSignals(indicators) {
    console.log('🎯 تحليل الإشارات...');
    
    const signals = [];
    let bullishCount = 0;
    let bearishCount = 0;
    
    // تحليل RSI
    if (indicators.rsi) {
        if (indicators.rsi > 70) {
            signals.push('RSI في ذروة الشراء (>70)');
            bearishCount += 2;
        } else if (indicators.rsi < 30) {
            signals.push('RSI في ذروة البيع (<30)');
            bullishCount += 2;
        } else if (indicators.rsi > 50) {
            signals.push('RSI إيجابي');
            bullishCount += 1;
        } else {
            signals.push('RSI سلبي');
            bearishCount += 1;
        }
    }
    
    // تحليل المتوسطات المتحركة
    if (indicators.sma20 && indicators.sma50) {
        if (indicators.sma20 > indicators.sma50) {
            signals.push('SMA20 فوق SMA50 (إشارة صعودية)');
            bullishCount += 1;
        } else {
            signals.push('SMA20 تحت SMA50 (إشارة هبوطية)');
            bearishCount += 1;
        }
    }
    
    if (indicators.ema20 && indicators.ema50) {
        if (indicators.ema20 > indicators.ema50) {
            signals.push('EMA20 فوق EMA50 (زخم صعودي)');
            bullishCount += 1;
        } else {
            signals.push('EMA20 تحت EMA50 (زخم هبوطي)');
            bearishCount += 1;
        }
    }
    
    // تحليل السعر مقابل المتوسطات
    if (indicators.currentPrice > indicators.sma20) {
        signals.push('السعر فوق SMA20');
        bullishCount += 0.5;
    } else {
        signals.push('السعر تحت SMA20');
        bearishCount += 0.5;
    }
    
    console.log('✅ تم تحليل الإشارات\n');
    
    return {
        signals,
        bullishCount,
        bearishCount,
        netSignal: bullishCount - bearishCount
    };
}

// إعطاء التوصية النهائية
function generateRecommendation(indicators, signalAnalysis) {
    console.log('📋 إعداد التوصية النهائية...');
    
    const netSignal = signalAnalysis.netSignal;
    const totalSignals = signalAnalysis.bullishCount + signalAnalysis.bearishCount;
    
    let decision = 'انتظر';
    let confidence = 50;
    let reasoning = [];
    
    if (Math.abs(netSignal) > totalSignals * 0.3) {
        if (netSignal > 0) {
            decision = 'شراء';
            confidence = Math.min(90, 50 + (netSignal / totalSignals) * 40);
            reasoning.push('الإشارات الفنية تدعم الاتجاه الصعودي');
        } else {
            decision = 'بيع';
            confidence = Math.min(90, 50 + (Math.abs(netSignal) / totalSignals) * 40);
            reasoning.push('الإشارات الفنية تدعم الاتجاه الهبوطي');
        }
    } else {
        reasoning.push('الإشارات متضاربة، من الأفضل الانتظار');
    }
    
    // توقع سعري بسيط
    const priceChange = netSignal * 0.01; // 1% لكل نقطة صافية
    const forecast24h = indicators.currentPrice * (1 + priceChange);
    const forecast48h = indicators.currentPrice * (1 + priceChange * 1.5);
    
    console.log('✅ تم إعداد التوصية\n');
    
    return {
        decision,
        confidence: Math.round(confidence),
        currentPrice: indicators.currentPrice,
        forecast24h,
        forecast48h,
        reasoning,
        riskLevel: Math.abs(netSignal) > 3 ? 'مرتفع' : Math.abs(netSignal) > 1 ? 'متوسط' : 'منخفض'
    };
}

// عرض النتائج
function displayResults(indicators, signalAnalysis, recommendation) {
    console.log('📊 === نتائج التحليل ===');
    console.log('========================\n');
    
    // معلومات السوق
    console.log('💰 معلومات السوق:');
    console.log(`   السعر الحالي: $${indicators.currentPrice.toLocaleString()}`);
    console.log(`   أعلى سعر (24ساعة): $${indicators.high24h.toLocaleString()}`);
    console.log(`   أقل سعر (24ساعة): $${indicators.low24h.toLocaleString()}`);
    console.log(`   حجم التداول (24ساعة): ${indicators.volume24h.toLocaleString()}\n`);
    
    // المؤشرات الفنية
    console.log('📈 المؤشرات الفنية:');
    console.log(`   RSI (14): ${indicators.rsi ? indicators.rsi.toFixed(1) : 'غير متوفر'}`);
    console.log(`   SMA20: $${indicators.sma20 ? indicators.sma20.toLocaleString() : 'غير متوفر'}`);
    console.log(`   SMA50: $${indicators.sma50 ? indicators.sma50.toLocaleString() : 'غير متوفر'}`);
    console.log(`   EMA20: $${indicators.ema20 ? indicators.ema20.toLocaleString() : 'غير متوفر'}`);
    console.log(`   EMA50: $${indicators.ema50 ? indicators.ema50.toLocaleString() : 'غير متوفر'}\n`);
    
    // الإشارات
    console.log('🔍 الإشارات المكتشفة:');
    signalAnalysis.signals.forEach((signal, index) => {
        console.log(`   ${index + 1}. ${signal}`);
    });
    console.log(`\n   إجمالي الإشارات الصعودية: ${signalAnalysis.bullishCount}`);
    console.log(`   إجمالي الإشارات الهبوطية: ${signalAnalysis.bearishCount}`);
    console.log(`   الإشارة الصافية: ${signalAnalysis.netSignal > 0 ? '+' : ''}${signalAnalysis.netSignal}\n`);
    
    // التوصية النهائية
    console.log('🎯 === التوصية النهائية ===');
    console.log(`   القرار: ${recommendation.decision}`);
    console.log(`   مستوى الثقة: ${recommendation.confidence}%`);
    console.log(`   مستوى المخاطرة: ${recommendation.riskLevel}`);
    console.log(`\n📅 التوقعات السعرية:`);
    console.log(`   خلال 24 ساعة: $${recommendation.forecast24h.toLocaleString()}`);
    console.log(`   خلال 48 ساعة: $${recommendation.forecast48h.toLocaleString()}`);
    
    const change24h = ((recommendation.forecast24h - recommendation.currentPrice) / recommendation.currentPrice) * 100;
    const change48h = ((recommendation.forecast48h - recommendation.currentPrice) / recommendation.currentPrice) * 100;
    
    console.log(`   التغيير المتوقع (24ساعة): ${change24h > 0 ? '+' : ''}${change24h.toFixed(2)}%`);
    console.log(`   التغيير المتوقع (48ساعة): ${change48h > 0 ? '+' : ''}${change48h.toFixed(2)}%`);
    
    console.log(`\n💡 الأسباب:`);
    recommendation.reasoning.forEach((reason, index) => {
        console.log(`   ${index + 1}. ${reason}`);
    });
    
    console.log('\n⚠️  تنويه: هذا التحليل للأغراض التعليمية فقط ولا يُعتبر نصيحة استثمارية.');
    console.log('========================\n');
}

// تشغيل التحليل التجريبي
async function runDemo() {
    try {
        console.log('🔄 بدء التحليل التجريبي...\n');
        
        // 1. إنشاء البيانات
        const mockData = generateMockData();
        
        // 2. حساب المؤشرات
        const indicators = calculateSimpleIndicators(mockData);
        
        // 3. تحليل الإشارات
        const signalAnalysis = analyzeSignals(indicators);
        
        // 4. إعداد التوصية
        const recommendation = generateRecommendation(indicators, signalAnalysis);
        
        // 5. عرض النتائج
        displayResults(indicators, signalAnalysis, recommendation);
        
        console.log('✅ تم إكمال التحليل التجريبي بنجاح!');
        console.log('\n🚀 لتشغيل التطبيق الكامل:');
        console.log('   1. تثبيت التبعيات: npm install');
        console.log('   2. تشغيل التطبيق: npm start');
        
    } catch (error) {
        console.error('❌ خطأ في التحليل التجريبي:', error.message);
    }
}

// تشغيل العرض التوضيحي
runDemo();
