# Bitcoin Analyzer Pro - محلل البتكوين الاحترافي

تطبيق سطح مكتب احترافي لتحليل سوق البتكوين باستخدام Node.js وElectron، يوفر تحليلاً شاملاً ودقيقاً مع توصيات استثمارية مبنية على أحدث المؤشرات الفنية.

## ✨ المميزات الرئيسية

### 📊 تحليل فني شامل
- **RSI (14)** - مؤشر القوة النسبية
- **MACD (12,26,9)** - تقارب وتباعد المتوسطات المتحركة
- **EMA (50, 200)** - المتوسطات المتحركة الأسية
- **SMA (20, 100)** - المتوسطات المتحركة البسيطة
- **Bollinger Bands (20,2)** - نطاقات بولينجر
- **ATR (14)** - متوسط المدى الحقيقي
- **ADX (14)** - مؤشر الاتجاه المتوسط
- **Stochastic Oscillator (14,3,3)** - مذبذب ستوكاستيك
- **OBV** - حجم التوازن
- **Ichimoku Cloud** - سحابة إيشيموكو
- **Fibonacci Retracement** - مستويات فيبوناتشي

### 🎯 توصيات ذكية
- **قرار نهائي**: شراء / بيع / انتظار
- **مستوى الثقة**: نسبة دقة التوصية
- **مستوى المخاطرة**: تقييم المخاطر
- **أسباب مفصلة**: شرح منطق القرار

### 📈 توقعات سعرية
- **توقع 24 ساعة**: السعر المتوقع خلال يوم
- **توقع 48 ساعة**: السعر المتوقع خلال يومين
- **نماذج متعددة**: انحدار خطي، متوسطات متحركة، زخم، تقلبات
- **دمج ذكي**: توقع مجمع من جميع النماذج

### 🔍 تحليل الأنماط
- **أنماط الشموع**: Doji, Hammer, Shooting Star, Engulfing, Morning/Evening Star
- **أنماط الرسم البياني**: Double Top/Bottom, Head & Shoulders, Triangle
- **تحليل الاتجاه**: قوة واتجاه السوق
- **مستويات الدعم والمقاومة**: نقاط مهمة في السوق

## 🚀 التثبيت والتشغيل

### متطلبات النظام
- **Node.js** 16.0 أو أحدث
- **npm** أو **yarn**
- **Windows 10/11**, **macOS 10.14+**, أو **Linux**
- اتصال بالإنترنت لجلب البيانات

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/yourusername/bitcoin-analyzer-electron.git
cd bitcoin-analyzer-electron
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تشغيل التطبيق**
```bash
npm start
```

### أوامر إضافية

```bash
# تشغيل في وضع التطوير
npm run dev

# بناء التطبيق للتوزيع
npm run dist

# بناء مجلد فقط (بدون installer)
npm run pack

# تشغيل الاختبارات
npm test
```

## 📁 هيكل المشروع

```
bitcoin-analyzer-electron/
│
├── package.json              # إعدادات المشروع والتبعيات
├── main.js                   # العملية الرئيسية لـ Electron
├── preload.js               # سكريبت preload للأمان
│
├── src/                     # ملفات الواجهة الأمامية
│   ├── index.html          # الواجهة الرئيسية
│   ├── renderer.js         # منطق الواجهة الأمامية
│   └── styles.css          # تصميم التطبيق
│
├── lib/                     # مكتبات التحليل
│   ├── fetcher.js          # جلب البيانات من APIs
│   ├── indicators.js       # حساب المؤشرات الفنية
│   ├── patterns.js         # تحليل الأنماط
│   ├── forecast.js         # التنبؤ السعري
│   └── signals.js          # خوارزمية القرار النهائية
│
└── README.md               # هذا الملف
```

## 🔧 كيفية عمل التطبيق

### 1. جلب البيانات
- يستخدم التطبيق **CoinGecko API** كمصدر أساسي
- **Binance API** كمصدر احتياطي
- جلب بيانات 72 ساعة الماضية (شمعة كل ساعة)
- التحقق من صحة البيانات قبل التحليل

### 2. حساب المؤشرات
- استخدام مكتبة `technicalindicators` المتخصصة
- حساب جميع المؤشرات بشكل متوازي
- معالجة الأخطاء لكل مؤشر منفرداً
- تخزين القيم الحالية والتاريخية

### 3. تحليل الأنماط
- فحص أنماط الشموع في آخر 10 شموع
- تحليل أنماط الرسم البياني في آخر 20-50 شمعة
- تحديد الاتجاه العام وقوته
- العثور على مستويات الدعم والمقاومة

### 4. التنبؤ السعري
- **نموذج الانحدار الخطي**: باستخدام مكتبة `regression`
- **نموذج المتوسط المتحرك**: بناءً على اتجاه المتوسطات
- **نموذج الزخم**: باستخدام RSI, MACD, Stochastic
- **نموذج التقلبات**: باستخدام ATR والتقلبات التاريخية
- **دمج النماذج**: متوسط مرجح بناءً على مستوى الثقة

### 5. اتخاذ القرار
- تجميع إشارات من جميع المؤشرات والأنماط
- حساب نقاط صعودية وهبوطية
- تحديد القرار النهائي بناءً على الأغلبية
- حساب مستوى الثقة والمخاطرة
- إعداد الأسباب والتفسيرات

## 📊 مصادر البيانات

### CoinGecko API (المصدر الأساسي)
- **السعر الحالي**: `/simple/price`
- **البيانات التاريخية**: `/coins/bitcoin/ohlc`
- **بيانات الحجم**: `/coins/bitcoin/market_chart`
- **مجاني** مع حدود معقولة

### Binance API (المصدر الاحتياطي)
- **البيانات التاريخية**: `/api/v3/klines`
- **إحصائيات 24 ساعة**: `/api/v3/ticker/24hr`
- **سرعة عالية** وموثوقية ممتازة

## ⚙️ الإعدادات والتخصيص

### تعديل فترات المؤشرات
في ملف `lib/indicators.js`:
```javascript
// RSI period
period: 14  // يمكن تغييرها إلى 21 أو 30

// MACD settings
fastPeriod: 12,
slowPeriod: 26,
signalPeriod: 9

// EMA periods
period: 50  // أو 200
```

### تعديل فترة البيانات
في ملف `lib/fetcher.js`:
```javascript
// تغيير من 72 ساعة إلى فترة أخرى
async function fetchBitcoinData(hours = 72)
```

### تعديل حساسية القرارات
في ملف `lib/signals.js`:
```javascript
// تعديل نسبة الأغلبية المطلوبة للقرار
if (Math.abs(netSignal) > totalSignals * 0.3) // 30%
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. خطأ في جلب البيانات**
```
Error: فشل في جلب البيانات من كلا المصدرين
```
- تحقق من اتصال الإنترنت
- تحقق من عدم حجب APIs من قبل Firewall
- انتظر قليلاً وأعد المحاولة (قد تكون APIs مؤقتاً غير متاحة)

**2. خطأ في حساب المؤشرات**
```
Error: البيانات غير كافية للتحليل
```
- تأكد من وجود 20 شمعة على الأقل
- تحقق من صحة البيانات (لا توجد قيم null أو سالبة)

**3. مشاكل في الواجهة**
- افتح Developer Tools: `Ctrl+Shift+I`
- تحقق من رسائل الخطأ في Console
- تأكد من تحميل جميع ملفات CSS و JS

**4. مشاكل في الأداء**
- أغلق التطبيقات الأخرى لتوفير ذاكرة
- تأكد من وجود مساحة كافية على القرص الصلب
- أعد تشغيل التطبيق إذا أصبح بطيئاً

## 📈 تطوير المشروع

### إضافة مؤشرات جديدة
1. أضف المؤشر في `lib/indicators.js`
2. أضف عرض المؤشر في `src/renderer.js`
3. أضف تصميم المؤشر في `src/styles.css`
4. أضف المؤشر في خوارزمية القرار `lib/signals.js`

### إضافة مصادر بيانات جديدة
1. أضف دالة جلب جديدة في `lib/fetcher.js`
2. أضف معالجة الأخطاء والتحويل
3. أضف المصدر الجديد في سلسلة المحاولات

### تحسين خوارزمية القرار
1. عدّل الأوزان في `lib/signals.js`
2. أضف شروط جديدة للقرارات
3. اختبر النتائج مع بيانات تاريخية

## 🔒 الأمان والخصوصية

- **لا يتم تخزين بيانات شخصية**: التطبيق لا يجمع أي معلومات شخصية
- **بيانات محلية فقط**: جميع العمليات تتم محلياً على جهازك
- **APIs عامة**: يستخدم فقط APIs عامة ومجانية
- **مفتوح المصدر**: يمكن مراجعة الكود بالكامل

## 📞 الدعم والمساعدة

### الإبلاغ عن مشاكل
- افتح issue جديد في GitHub
- اذكر نظام التشغيل وإصدار Node.js
- أرفق رسالة الخطأ كاملة
- اذكر خطوات إعادة إنتاج المشكلة

### طلب مميزات جديدة
- افتح feature request في GitHub
- اشرح المميزة المطلوبة بالتفصيل
- اذكر الفائدة المتوقعة من المميزة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- **TechnicalIndicators.js** - مكتبة المؤشرات الفنية
- **Chart.js** - مكتبة الرسوم البيانية
- **Electron** - إطار عمل التطبيقات
- **CoinGecko & Binance** - مصادر البيانات

---

**تنويه**: هذا التطبيق للأغراض التعليمية والتحليلية فقط. لا يُعتبر نصيحة استثمارية. استشر خبير مالي قبل اتخاذ قرارات استثمارية.
