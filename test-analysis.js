// اختبار التحليل الفعلي للبتكوين
const { analyzeMarket } = require('./lib/signals');

console.log('🚀 اختبار التحليل الفعلي للبتكوين');
console.log('=====================================\n');

async function testRealAnalysis() {
    try {
        console.log('📡 جاري الاتصال بـ APIs وجلب البيانات الحقيقية...');
        console.log('⏳ هذا قد يستغرق بضع ثوانٍ...\n');
        
        const startTime = Date.now();
        
        // تشغيل التحليل الكامل
        const analysis = await analyzeMarket();
        
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log(`✅ تم إكمال التحليل في ${duration.toFixed(2)} ثانية\n`);
        
        // عرض النتائج
        displayAnalysisResults(analysis);
        
    } catch (error) {
        console.error('❌ خطأ في التحليل:', error.message);
        console.log('\n🔧 نصائح لحل المشكلة:');
        console.log('1. تحقق من اتصال الإنترنت');
        console.log('2. تأكد من عدم حجب APIs');
        console.log('3. أعد المحاولة بعد قليل');
        console.log('4. جرب العرض التوضيحي: node demo.js');
    }
}

function displayAnalysisResults(analysis) {
    console.log('📊 === نتائج التحليل الحقيقي ===');
    console.log('=================================\n');
    
    // معلومات السوق
    if (analysis.marketData) {
        const market = analysis.marketData;
        console.log('💰 معلومات السوق:');
        console.log(`   السعر الحالي: $${market.currentPrice.toLocaleString()}`);
        console.log(`   التغيير (24ساعة): ${market.change24h > 0 ? '+' : ''}${market.change24h.toFixed(2)}%`);
        console.log(`   حجم التداول (24ساعة): ${market.volume24h.toLocaleString()}`);
        console.log(`   آخر تحديث: ${new Date(market.lastUpdate).toLocaleString('ar-SA')}\n`);
    }
    
    // المؤشرات الفنية
    if (analysis.indicators) {
        const ind = analysis.indicators;
        console.log('📈 المؤشرات الفنية:');
        
        if (ind.currentRSI) {
            console.log(`   RSI (14): ${ind.currentRSI.toFixed(1)}`);
        }
        
        if (ind.currentMACD) {
            console.log(`   MACD: ${ind.currentMACD.macd.toFixed(4)}`);
            console.log(`   MACD Signal: ${ind.currentMACD.signal.toFixed(4)}`);
            console.log(`   MACD Histogram: ${ind.currentMACD.histogram.toFixed(4)}`);
        }
        
        if (ind.currentEMA50 && ind.currentEMA200) {
            console.log(`   EMA50: $${ind.currentEMA50.toLocaleString()}`);
            console.log(`   EMA200: $${ind.currentEMA200.toLocaleString()}`);
        }
        
        if (ind.currentBB) {
            console.log(`   Bollinger Upper: $${ind.currentBB.upper.toLocaleString()}`);
            console.log(`   Bollinger Lower: $${ind.currentBB.lower.toLocaleString()}`);
            console.log(`   BB Position: ${ind.currentBB.position}`);
        }
        
        if (ind.currentStochastic) {
            console.log(`   Stochastic K: ${ind.currentStochastic.k.toFixed(1)}`);
            console.log(`   Stochastic D: ${ind.currentStochastic.d.toFixed(1)}`);
        }
        
        if (ind.currentADX !== null && ind.currentADX !== undefined) {
            console.log(`   ADX: ${ind.currentADX.toFixed(1)}`);
        }
        
        console.log();
    }
    
    // التوقعات السعرية
    if (analysis.forecast && analysis.forecast.combined) {
        const forecast = analysis.forecast.combined;
        console.log('🔮 التوقعات السعرية:');
        console.log(`   خلال 24 ساعة: $${forecast.next24h.toLocaleString()}`);
        console.log(`   خلال 48 ساعة: $${forecast.next48h.toLocaleString()}`);
        console.log(`   التغيير المتوقع (24ساعة): ${forecast.change24h > 0 ? '+' : ''}${forecast.change24h.toFixed(2)}%`);
        console.log(`   التغيير المتوقع (48ساعة): ${forecast.change48h > 0 ? '+' : ''}${forecast.change48h.toFixed(2)}%`);
        console.log(`   مستوى الثقة: ${(forecast.confidence * 100).toFixed(1)}%`);
        console.log(`   الاتجاه: ${forecast.direction}`);
        console.log(`   القوة: ${forecast.strength}\n`);
    }
    
    // الأنماط المكتشفة
    if (analysis.patterns) {
        console.log('🔍 الأنماط المكتشفة:');
        
        if (analysis.patterns.candlestickPatterns && analysis.patterns.candlestickPatterns.length > 0) {
            console.log('   أنماط الشموع:');
            analysis.patterns.candlestickPatterns.forEach((pattern, index) => {
                console.log(`     ${index + 1}. ${pattern.type} (${pattern.signal}) - ${pattern.description}`);
            });
        }
        
        if (analysis.patterns.chartPatterns && analysis.patterns.chartPatterns.length > 0) {
            console.log('   أنماط الرسم البياني:');
            analysis.patterns.chartPatterns.forEach((pattern, index) => {
                console.log(`     ${index + 1}. ${pattern.type} (${pattern.signal}) - ${pattern.description}`);
            });
        }
        
        if (analysis.patterns.trendAnalysis) {
            const trend = analysis.patterns.trendAnalysis;
            console.log(`   الاتجاه العام: ${trend.direction} (${trend.strength})`);
        }
        
        console.log();
    }
    
    // الإشارات
    if (analysis.signals && analysis.signals.overall) {
        const signals = analysis.signals.overall;
        console.log('📊 تحليل الإشارات:');
        console.log(`   إجمالي الإشارات الصعودية: ${signals.totalBullish}`);
        console.log(`   إجمالي الإشارات الهبوطية: ${signals.totalBearish}`);
        console.log(`   الإشارة الصافية: ${signals.netSignal > 0 ? '+' : ''}${signals.netSignal}`);
        console.log(`   قوة الإشارة: ${signals.strength}`);
        console.log(`   مستوى الثقة: ${(signals.confidence * 100).toFixed(1)}%\n`);
    }
    
    // التوصية النهائية
    if (analysis.recommendation) {
        const rec = analysis.recommendation;
        console.log('🎯 === التوصية النهائية ===');
        console.log(`   القرار: ${rec.decision}`);
        console.log(`   مستوى الثقة: ${rec.confidence}%`);
        console.log(`   مستوى المخاطرة: ${rec.riskLevel}`);
        
        if (rec.targetPrice24h) {
            console.log(`   السعر المستهدف (24ساعة): $${rec.targetPrice24h.toLocaleString()}`);
        }
        
        if (rec.targetPrice48h) {
            console.log(`   السعر المستهدف (48ساعة): $${rec.targetPrice48h.toLocaleString()}`);
        }
        
        console.log('\n💡 الأسباب:');
        if (rec.reasons && rec.reasons.length > 0) {
            rec.reasons.forEach((reason, index) => {
                console.log(`   ${index + 1}. ${reason}`);
            });
        }
        
        if (rec.summary) {
            console.log(`\n📝 الملخص: ${rec.summary}`);
        }
        
        console.log('\n⚠️  تنويه: هذا التحليل للأغراض التعليمية فقط ولا يُعتبر نصيحة استثمارية.');
    }
    
    console.log('\n=================================');
    console.log('✅ تم إكمال عرض النتائج بنجاح!');
}

// تشغيل الاختبار
testRealAnalysis();
