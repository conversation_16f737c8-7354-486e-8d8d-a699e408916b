# 🔧 دليل حل مشكلة "Cannot read properties of undefined (reading 'analyzeMarket')"

## 🔍 تحليل المشكلة

المشكلة التي تظهر في الصورة هي:
```
Cannot read properties of undefined (reading 'analyzeMarket')
```

هذا يعني أن `window.api.analyzeMarket` غير متوفر في الواجهة الأمامية.

## 🎯 الأسباب المحتملة

### 1. مشكلة في preload.js
- عدم تحميل preload.js بشكل صحيح
- خطأ في contextBridge.exposeInMainWorld
- مشكلة في إعدادات webPreferences

### 2. مشكلة في main.js
- عدم تحديد مسار preload بشكل صحيح
- إعدادات contextIsolation أو nodeIntegration خاطئة
- عدم تسجيل ipcMain handlers

### 3. مشكلة في renderer.js
- محاولة استخدام window.api قبل تحميله
- عدم التحقق من وجود window.api

## ✅ الحلول المقترحة

### الحل الأول: إصلاح preload.js

```javascript
const { contextBridge, ipcRenderer } = require('electron');

console.log('🔧 تحميل preload.js...');

// التحقق من توفر المكونات
if (!contextBridge) {
  console.error('❌ contextBridge غير متوفر');
  throw new Error('contextBridge غير متوفر');
}

if (!ipcRenderer) {
  console.error('❌ ipcRenderer غير متوفر');
  throw new Error('ipcRenderer غير متوفر');
}

try {
  const api = {
    analyzeMarket: async () => {
      console.log('📡 استدعاء analyzeMarket...');
      return await ipcRenderer.invoke('analyze-market');
    },
    
    checkConnection: async () => {
      console.log('🌐 فحص الاتصال...');
      return await ipcRenderer.invoke('check-connection');
    },
    
    test: () => {
      console.log('🧪 اختبار API');
      return 'API يعمل بشكل صحيح';
    }
  };

  contextBridge.exposeInMainWorld('api', api);
  console.log('✅ تم تعريض API بنجاح');
  
} catch (error) {
  console.error('❌ خطأ في تعريض API:', error);
}
```

### الحل الثاني: التحقق من إعدادات main.js

```javascript
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    webPreferences: {
      nodeIntegration: false,        // مهم: يجب أن يكون false
      contextIsolation: true,        // مهم: يجب أن يكون true
      preload: path.join(__dirname, 'preload.js'), // مسار صحيح
      devTools: true                 // للتشخيص
    }
  });

  mainWindow.loadFile('src/index.html');
  
  // فتح أدوات المطور للتشخيص
  mainWindow.webContents.openDevTools();
}

// تسجيل معالجات IPC
ipcMain.handle('analyze-market', async () => {
  try {
    const { analyzeMarket } = require('./lib/signals');
    const result = await analyzeMarket();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});
```

### الحل الثالث: تحسين renderer.js

```javascript
// التحقق من توفر API قبل الاستخدام
async function startAnalysis() {
  try {
    // التحقق من توفر window.api
    if (!window.api) {
      throw new Error('API غير متوفر. يرجى إعادة تشغيل التطبيق.');
    }
    
    if (!window.api.analyzeMarket) {
      throw new Error('دالة analyzeMarket غير متوفرة.');
    }
    
    console.log('📡 بدء التحليل...');
    const result = await window.api.analyzeMarket();
    
    if (result && result.success) {
      // عرض النتائج
      displayResults(result.data);
    } else {
      throw new Error(result?.error || 'فشل في التحليل');
    }
    
  } catch (error) {
    console.error('❌ خطأ في التحليل:', error);
    showErrorState(error.message);
  }
}

// التحقق من توفر API عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  console.log('📄 تحميل الصفحة...');
  
  // انتظار قليل للتأكد من تحميل preload
  setTimeout(() => {
    if (!window.api) {
      console.error('❌ window.api غير متوفر');
      showErrorState('خطأ في تحميل التطبيق. يرجى إعادة تشغيل التطبيق.');
    } else {
      console.log('✅ window.api متوفر:', Object.keys(window.api));
    }
  }, 100);
});
```

## 🧪 خطوات التشخيص

### 1. فحص Console في أدوات المطور
```javascript
// في Console أدوات المطور، اكتب:
console.log('window.api:', window.api);
console.log('typeof window.api:', typeof window.api);
if (window.api) {
  console.log('API keys:', Object.keys(window.api));
}
```

### 2. اختبار preload.js
```bash
# تشغيل التطبيق مع تسجيل مفصل
npm run debug
```

### 3. اختبار مكتبة signals
```bash
# اختبار مكتبة التحليل
node test-api.js
```

### 4. اختبار الواجهة
```bash
# تشغيل واجهة اختبار مبسطة
npm run test-ui
```

## 🔧 الإصلاح السريع

### الخطوة 1: تشغيل ملف الإصلاح
```bash
fix-analyzer-issues.bat
```

### الخطوة 2: اختيار الحل المناسب
1. **تشغيل في وضع التشخيص** - لرؤية الأخطاء بالتفصيل
2. **إعادة بناء مكتبة التحليل** - إذا كانت المشكلة في lib/signals.js
3. **تنظيف التبعيات** - إذا كانت المشكلة في npm packages

### الخطوة 3: التحقق من النتيجة
- فتح أدوات المطور (F12)
- مراقبة رسائل Console
- اختبار زر "حلل الآن"

## 📋 قائمة التحقق

### ✅ ملفات مطلوبة:
- [ ] main.js موجود ويحتوي على ipcMain handlers
- [ ] preload.js موجود ويستخدم contextBridge
- [ ] lib/signals.js موجود ويحتوي على analyzeMarket
- [ ] src/renderer.js يتحقق من window.api

### ✅ إعدادات صحيحة:
- [ ] webPreferences.nodeIntegration = false
- [ ] webPreferences.contextIsolation = true
- [ ] webPreferences.preload يشير للمسار الصحيح
- [ ] contextBridge.exposeInMainWorld يعمل بشكل صحيح

### ✅ اختبارات:
- [ ] window.api متوفر في Console
- [ ] window.api.test() يعمل
- [ ] window.api.analyzeMarket متوفر
- [ ] node test-api.js يعمل بدون أخطاء

## 🚀 بعد الإصلاح

### إعادة بناء التطبيق:
```bash
npm run build:win
```

### اختبار النسخة المبنية:
```bash
"dist\win-unpacked\Bitcoin Analyzer Pro.exe"
```

## 📞 إذا استمرت المشكلة

### 1. تحقق من سجل الأخطاء:
- افتح أدوات المطور (F12)
- انتقل لتبويب Console
- ابحث عن رسائل خطأ حمراء

### 2. تحقق من ملفات النظام:
- تأكد من أن Windows Defender لا يحجب الملفات
- تأكد من وجود صلاحيات كافية
- جرب تشغيل التطبيق كمدير

### 3. إعادة تثبيت:
```bash
# حذف node_modules وإعادة التثبيت
rmdir /s node_modules
del package-lock.json
npm install
```

---

**💡 نصيحة:** المشكلة الأكثر شيوعاً هي عدم تحميل preload.js بشكل صحيح. تأكد من أن مسار preload في main.js صحيح وأن contextBridge يعمل.
