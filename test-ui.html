<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API - محلل البتكوين</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API - محلل البتكوين الاحترافي</h1>
        
        <div class="test-section">
            <h3>1. اختبار توفر API</h3>
            <button onclick="testAPIAvailability()">اختبار توفر API</button>
            <div id="apiTest" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. اختبار دالة الاختبار</h3>
            <button onclick="testFunction()">اختبار دالة test()</button>
            <div id="testResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. اختبار فحص الاتصال</h3>
            <button onclick="testConnection()">فحص الاتصال</button>
            <div id="connectionResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. اختبار تحليل السوق</h3>
            <button onclick="testAnalysis()">تحليل السوق</button>
            <div id="analysisResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>5. اختبار شامل</h3>
            <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
            <div id="allTestsResult" class="result"></div>
        </div>
    </div>

    <script>
        console.log('🚀 تحميل صفحة الاختبار...');
        
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }
        
        function showLoading(elementId, message = 'جاري التحميل...') {
            showResult(elementId, message, 'loading');
        }
        
        async function testAPIAvailability() {
            console.log('🔍 اختبار توفر API...');
            
            try {
                if (typeof window === 'undefined') {
                    throw new Error('window غير متوفر');
                }
                
                if (!window.api) {
                    throw new Error('window.api غير متوفر');
                }
                
                const apiKeys = Object.keys(window.api);
                const message = `✅ API متوفر!\nالدوال المتوفرة: ${apiKeys.join(', ')}`;
                showResult('apiTest', message, 'success');
                
            } catch (error) {
                const message = `❌ خطأ في API: ${error.message}`;
                showResult('apiTest', message, 'error');
                console.error('خطأ في اختبار API:', error);
            }
        }
        
        async function testFunction() {
            console.log('🧪 اختبار دالة test...');
            showLoading('testResult');
            
            try {
                if (!window.api || !window.api.test) {
                    throw new Error('دالة test غير متوفرة');
                }
                
                const result = window.api.test();
                const message = `✅ دالة test تعمل!\nالنتيجة: ${result}`;
                showResult('testResult', message, 'success');
                
            } catch (error) {
                const message = `❌ خطأ في دالة test: ${error.message}`;
                showResult('testResult', message, 'error');
                console.error('خطأ في دالة test:', error);
            }
        }
        
        async function testConnection() {
            console.log('🌐 اختبار الاتصال...');
            showLoading('connectionResult', 'فحص الاتصال...');
            
            try {
                if (!window.api || !window.api.checkConnection) {
                    throw new Error('دالة checkConnection غير متوفرة');
                }
                
                const result = await window.api.checkConnection();
                
                if (result.connected) {
                    const message = `✅ الاتصال يعمل بشكل صحيح!`;
                    showResult('connectionResult', message, 'success');
                } else {
                    const message = `❌ مشكلة في الاتصال: ${result.error || 'خطأ غير معروف'}`;
                    showResult('connectionResult', message, 'error');
                }
                
            } catch (error) {
                const message = `❌ خطأ في فحص الاتصال: ${error.message}`;
                showResult('connectionResult', message, 'error');
                console.error('خطأ في فحص الاتصال:', error);
            }
        }
        
        async function testAnalysis() {
            console.log('📊 اختبار تحليل السوق...');
            showLoading('analysisResult', 'جاري التحليل... قد يستغرق بضع ثوان');
            
            try {
                if (!window.api || !window.api.analyzeMarket) {
                    throw new Error('دالة analyzeMarket غير متوفرة');
                }
                
                const startTime = Date.now();
                const result = await window.api.analyzeMarket();
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                if (result && result.success) {
                    const data = result.data;
                    const price = data.recommendation?.currentPrice || 'غير متوفر';
                    const recommendation = data.recommendation?.decision || 'غير متوفر';
                    const confidence = data.recommendation?.confidence || 'غير متوفر';
                    
                    const message = `✅ التحليل نجح! (${duration}ms)
السعر الحالي: $${price}
التوصية: ${recommendation}
مستوى الثقة: ${confidence}%`;
                    showResult('analysisResult', message, 'success');
                } else {
                    const errorMsg = result?.error || 'لا توجد بيانات';
                    const message = `❌ فشل التحليل: ${errorMsg}`;
                    showResult('analysisResult', message, 'error');
                }
                
            } catch (error) {
                const message = `❌ خطأ في التحليل: ${error.message}`;
                showResult('analysisResult', message, 'error');
                console.error('خطأ في التحليل:', error);
            }
        }
        
        async function runAllTests() {
            console.log('🔄 تشغيل جميع الاختبارات...');
            showLoading('allTestsResult', 'تشغيل جميع الاختبارات...');
            
            const results = [];
            
            try {
                // اختبار 1: توفر API
                try {
                    await testAPIAvailability();
                    results.push('✅ توفر API: نجح');
                } catch (error) {
                    results.push(`❌ توفر API: فشل (${error.message})`);
                }
                
                // اختبار 2: دالة test
                try {
                    await testFunction();
                    results.push('✅ دالة test: نجح');
                } catch (error) {
                    results.push(`❌ دالة test: فشل (${error.message})`);
                }
                
                // اختبار 3: الاتصال
                try {
                    await testConnection();
                    results.push('✅ فحص الاتصال: نجح');
                } catch (error) {
                    results.push(`❌ فحص الاتصال: فشل (${error.message})`);
                }
                
                // اختبار 4: التحليل
                try {
                    await testAnalysis();
                    results.push('✅ تحليل السوق: نجح');
                } catch (error) {
                    results.push(`❌ تحليل السوق: فشل (${error.message})`);
                }
                
                const message = `🏁 انتهت جميع الاختبارات:\n\n${results.join('\n')}`;
                showResult('allTestsResult', message, 'info');
                
            } catch (error) {
                const message = `❌ خطأ في تشغيل الاختبارات: ${error.message}`;
                showResult('allTestsResult', message, 'error');
            }
        }
        
        // تشغيل اختبار أولي عند تحميل الصفحة
        window.addEventListener('DOMContentLoaded', () => {
            console.log('📄 تم تحميل الصفحة');
            setTimeout(() => {
                testAPIAvailability();
            }, 500);
        });
    </script>
</body>
</html>
