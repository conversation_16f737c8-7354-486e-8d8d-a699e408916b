@echo off
chcp 65001 >nul
echo.
echo ===============================================
echo    اختبار الملفات المبنية - محلل البتكوين
echo ===============================================
echo.

:: التحقق من وجود مجلد dist
if not exist "dist" (
    echo ❌ مجلد dist غير موجود
    echo يرجى بناء التطبيق أولاً باستخدام: build.bat
    pause
    exit /b 1
)

echo 📁 محتويات مجلد dist:
echo.
dir /b dist\*.exe 2>nul
if %errorlevel% neq 0 (
    echo ❌ لا توجد ملفات .exe في مجلد dist
    echo يرجى بناء التطبيق أولاً
    pause
    exit /b 1
)

echo.
echo 🔍 الملفات المتوفرة:
echo.

:: التحقق من ملف installer
if exist "dist\Bitcoin Analyzer Pro-1.0.0-x64.exe" (
    echo ✅ Installer: Bitcoin Analyzer Pro-1.0.0-x64.exe
    for %%A in ("dist\Bitcoin Analyzer Pro-1.0.0-x64.exe") do echo    الحجم: %%~zA bytes
) else (
    echo ❌ Installer غير موجود
)

:: التحقق من ملف portable
if exist "dist\Bitcoin Analyzer Pro-1.0.0-Portable.exe" (
    echo ✅ Portable: Bitcoin Analyzer Pro-1.0.0-Portable.exe
    for %%A in ("dist\Bitcoin Analyzer Pro-1.0.0-Portable.exe") do echo    الحجم: %%~zA bytes
) else (
    echo ❌ Portable غير موجود
)

:: التحقق من مجلد unpacked
if exist "dist\win-unpacked\Bitcoin Analyzer Pro.exe" (
    echo ✅ Unpacked: dist\win-unpacked\Bitcoin Analyzer Pro.exe
    for %%A in ("dist\win-unpacked\Bitcoin Analyzer Pro.exe") do echo    الحجم: %%~zA bytes
) else (
    echo ❌ Unpacked غير موجود
)

echo.
echo 🧪 اختبار الملفات:
echo.
echo 1. اختبار النسخة المفكوكة (Unpacked)
echo 2. اختبار النسخة المحمولة (Portable)
echo 3. عرض معلومات الملفات
echo 4. فتح مجلد dist
echo 5. إنشاء حزمة توزيع
echo 6. خروج
echo.
set /p choice="اختر رقم (1-6): "

if "%choice%"=="1" goto test_unpacked
if "%choice%"=="2" goto test_portable
if "%choice%"=="3" goto show_info
if "%choice%"=="4" goto open_dist
if "%choice%"=="5" goto create_package
if "%choice%"=="6" goto :eof
echo ❌ اختيار غير صحيح
pause
goto :eof

:test_unpacked
echo.
echo 🧪 اختبار النسخة المفكوكة...
if exist "dist\win-unpacked\Bitcoin Analyzer Pro.exe" (
    echo تشغيل التطبيق...
    start "" "dist\win-unpacked\Bitcoin Analyzer Pro.exe"
    echo ✅ تم تشغيل التطبيق
    echo تحقق من أن التطبيق يعمل بشكل صحيح
) else (
    echo ❌ الملف غير موجود
)
pause
goto :eof

:test_portable
echo.
echo 🧪 اختبار النسخة المحمولة...
if exist "dist\Bitcoin Analyzer Pro-1.0.0-Portable.exe" (
    echo تشغيل النسخة المحمولة...
    start "" "dist\Bitcoin Analyzer Pro-1.0.0-Portable.exe"
    echo ✅ تم تشغيل النسخة المحمولة
    echo تحقق من أن التطبيق يعمل بشكل صحيح
) else (
    echo ❌ الملف غير موجود
)
pause
goto :eof

:show_info
echo.
echo 📋 معلومات تفصيلية:
echo.

if exist "dist\Bitcoin Analyzer Pro-1.0.0-x64.exe" (
    echo 📦 Installer:
    echo    الاسم: Bitcoin Analyzer Pro-1.0.0-x64.exe
    for %%A in ("dist\Bitcoin Analyzer Pro-1.0.0-x64.exe") do (
        echo    الحجم: %%~zA bytes
        echo    التاريخ: %%~tA
    )
    echo    النوع: NSIS Installer
    echo    الوصف: ملف تثبيت يقوم بتثبيت التطبيق في النظام
    echo.
)

if exist "dist\Bitcoin Analyzer Pro-1.0.0-Portable.exe" (
    echo 💼 Portable:
    echo    الاسم: Bitcoin Analyzer Pro-1.0.0-Portable.exe
    for %%A in ("dist\Bitcoin Analyzer Pro-1.0.0-Portable.exe") do (
        echo    الحجم: %%~zA bytes
        echo    التاريخ: %%~tA
    )
    echo    النوع: Portable Executable
    echo    الوصف: نسخة محمولة لا تحتاج تثبيت
    echo.
)

if exist "dist\win-unpacked" (
    echo 📂 Unpacked Directory:
    echo    المسار: dist\win-unpacked\
    for /f %%A in ('dir /s /b "dist\win-unpacked" ^| find /c /v ""') do echo    عدد الملفات: %%A
    echo    الوصف: مجلد التطبيق المفكوك للاختبار
    echo.
)

echo 🎯 متطلبات النظام:
echo    نظام التشغيل: Windows 10/11 (x64)
echo    الذاكرة: 4GB RAM (مستحسن)
echo    المساحة: 500MB
echo    الإنترنت: مطلوب لجلب البيانات

pause
goto :eof

:open_dist
echo.
echo 📁 فتح مجلد dist...
start "" "dist"
echo ✅ تم فتح مجلد dist
pause
goto :eof

:create_package
echo.
echo 📦 إنشاء حزمة توزيع...
echo.

:: إنشاء مجلد التوزيع
set "package_name=Bitcoin-Analyzer-Pro-v1.0.0"
if exist "%package_name%" rmdir /s /q "%package_name%"
mkdir "%package_name%"

:: نسخ الملفات
echo نسخ الملفات...
if exist "dist\Bitcoin Analyzer Pro-1.0.0-x64.exe" (
    copy "dist\Bitcoin Analyzer Pro-1.0.0-x64.exe" "%package_name%\"
    echo ✅ تم نسخ Installer
)

if exist "dist\Bitcoin Analyzer Pro-1.0.0-Portable.exe" (
    copy "dist\Bitcoin Analyzer Pro-1.0.0-Portable.exe" "%package_name%\"
    echo ✅ تم نسخ Portable
)

:: نسخ ملف التعليمات
if exist "dist\تعليمات التشغيل.txt" (
    copy "dist\تعليمات التشغيل.txt" "%package_name%\"
    echo ✅ تم نسخ التعليمات
)

:: نسخ ملفات إضافية
copy "README.md" "%package_name%\" 2>nul
copy "QUICKSTART.md" "%package_name%\" 2>nul

echo.
echo ✅ تم إنشاء حزمة التوزيع: %package_name%\
echo.
echo 📋 محتويات الحزمة:
dir /b "%package_name%"

echo.
echo 🚀 الحزمة جاهزة للتوزيع!
echo يمكنك ضغط المجلد وتوزيعه

:: فتح مجلد الحزمة
start "" "%package_name%"

pause
goto :eof
