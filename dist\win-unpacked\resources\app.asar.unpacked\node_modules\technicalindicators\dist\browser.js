(function(e){"use strict";function t(e){return ne[e]}function r(e){var r=t("precision");return r?parseFloat(e.toPrecision(r)):e}function o(e){se.reverseInputs(e);var t=new pe(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function l(e){se.reverseInputs(e);var t=new ie(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function n(e){se.reverseInputs(e);var t=new he(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function a(e){se.reverseInputs(e);var t=new ge(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function s(e){se.reverseInputs(e);var t=new ce(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function u(e){se.reverseInputs(e);var t=new de(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function p(e){se.reverseInputs(e);var t=new me(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function i(e){se.reverseInputs(e);var t=new we(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function h(e){se.reverseInputs(e);var t=new Pe(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function g(e){se.reverseInputs(e);var t=new Oe(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function v(e){se.reverseInputs(e);var t=new Ae(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function c(e){se.reverseInputs(e);var t=new Le(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function _(e){se.reverseInputs(e);var t=new Te(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function d(e){se.reverseInputs(e);var t=new Me(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function x(e){se.reverseInputs(e);var t=new je(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function m(e){se.reverseInputs(e);var t=new We(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function f(e){se.reverseInputs(e);var t=new Ke(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function w(e){se.reverseInputs(e);var t=new Ne(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function y(e){se.reverseInputs(e);var t=new Qe(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function P(e){se.reverseInputs(e);var t=new Ze(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function k(e){se.reverseInputs(e);var t=new Je(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function I(e){se.reverseInputs(e);var t=new tt(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function O(e){se.reverseInputs(e);var t=new ot(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function V(e){se.reverseInputs(e);var t=new nt(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function b(e){se.reverseInputs(e);var t=new st(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function C(e,t,r,o){return e<=r&&t>=r||r<=e&&o>=e}function q(e){se.reverseInputs(e);var t=new pt(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function E(e){se.reverseInputs(e);var t=new vt(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function S(e){se.reverseInputs(e);var t=new _t(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function A(e){se.reverseInputs(e);var t=new xt(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function B(e){se.reverseInputs(e);var t=new ft(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function H(e){se.reverseInputs(e);var t=new yt(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function L(e){se.reverseInputs(e);var t=new kt(e).result;return e.reversedInput&&(t.open.reverse(),t.high.reverse(),t.low.reverse(),t.close.reverse(),t.volume.reverse(),t.timestamp.reverse()),se.reverseInputs(e),t}function z(e){se.reverseInputs(e);var t=new Ot(e).result;return e.reversedInput&&(t.open.reverse(),t.high.reverse(),t.low.reverse(),t.close.reverse(),t.volume.reverse(),t.timestamp.reverse()),se.reverseInputs(e),t}function T(e){return new Tt().hasPattern(e)}function D(e){return new Dt().hasPattern(e)}function M(e){return new Mt().hasPattern(e)}function R(e){return new Rt().hasPattern(e)}function j(e){se.reverseInputs(e);var t=new vr(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function F(e){se.reverseInputs(e);var t=new dr(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function W(e){se.reverseInputs(e);var t=new fr(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function U(e){se.reverseInputs(e);var t=new yr(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function K(e){se.reverseInputs(e);var t=new kr(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t}function G(){return["sma","ema","wma","wema","macd","rsi","bollingerbands","adx","atr","truerange","roc","kst","psar","stochastic","williamsr","adl","obv","trix","cci","awesomeoscillator","forceindex","vwap","volumeprofile","renko","heikinashi","stochasticrsi","mfi","averagegain","averageloss","highest","lowest","sum","FixedSizeLinkedList","sd","bullish","bearish","abandonedbaby","doji","bearishengulfingpattern","bullishengulfingpattern","darkcloudcover","downsidetasukigap","dragonflydoji","gravestonedoji","bullishharami","bearishharami","bullishharamicross","bearishharamicross","eveningdojistar","eveningstar","morningdojistar","morningstar","bullishmarubozu","bearishmarubozu","piercingline","bullishspinningtop","bearishspinningtop","threeblackcrows","threewhitesoldiers","bullishhammerstick","bearishhammerstick","bullishinvertedhammerstick","bearishinvertedhammerstick","hammerpattern","hammerpatternunconfirmed","hangingman","hangingmanunconfirmed","shootingstar","shootingstarunconfirmed","tweezertop","tweezerbottom","ichimokucloud","keltnerchannels","chandelierexit","crossup","crossdown","crossover"]}var N=Math.min,X=Math.max,Q=Math.abs,Y=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},Z=function(){function e(e,t){for(var r,o=0;o<t.length;o++)r=t[o],r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),$=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)},J=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&("object"==typeof t||"function"==typeof t)?t:e},ee=function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)},te=function e(t,r,o){Y(this,e),this.next=o,o&&(o.prev=this),this.prev=r,r&&(r.next=this),this.data=t},re=function(){function e(){Y(this,e),this._length=0}return Z(e,[{key:"push",value:function(e){this._tail=new te(e,this._tail),0===this._length&&(this._head=this._tail,this._current=this._head,this._next=this._head),this._length++}},{key:"pop",value:function(){var e=this._tail;if(0!==this._length)return(this._length--,0===this._length)?(this._head=this._tail=this._current=this._next=void 0,e.data):(this._tail=e.prev,this._tail.next=void 0,this._current===e&&(this._current=this._tail,this._next=void 0),e.data)}},{key:"shift",value:function(){var e=this._head;if(0!==this._length)return(this._length--,0===this._length)?(this._head=this._tail=this._current=this._next=void 0,e.data):(this._head=this._head.next,this._current===e&&(this._current=this._head,this._next=this._current.next),e.data)}},{key:"unshift",value:function(e){this._head=new te(e,void 0,this._head),0===this._length&&(this._tail=this._head,this._next=this._head),this._length++}},{key:"unshiftCurrent",value:function(){var e=this._current;return e===this._head||2>this._length?e&&e.data:(e===this._tail?(this._tail=e.prev,this._tail.next=void 0,this._current=this._tail):(e.next.prev=e.prev,e.prev.next=e.next,this._current=e.prev),this._next=this._current.next,e.next=this._head,e.prev=void 0,this._head.prev=e,this._head=e,e.data)}},{key:"removeCurrent",value:function(){var e=this._current;if(0!==this._length)return(this._length--,0===this._length)?(this._head=this._tail=this._current=this._next=void 0,e.data):(e===this._tail?(this._tail=e.prev,this._tail.next=void 0,this._current=this._tail):e===this._head?(this._head=e.next,this._head.prev=void 0,this._current=this._head):(e.next.prev=e.prev,e.prev.next=e.next,this._current=e.prev),this._next=this._current.next,e.data)}},{key:"resetCursor",value:function(){return this._current=this._next=this._head,this}},{key:"next",value:function(){var e=this._next;if(void 0!==e)return this._next=e.next,this._current=e,e.data}},{key:"head",get:function(){return this._head&&this._head.data}},{key:"tail",get:function(){return this._tail&&this._tail.data}},{key:"current",get:function(){return this._current&&this._current.data}},{key:"length",get:function(){return this._length}}]),e}(),oe=function(e){function t(e,r,o,l){Y(this,t);var n=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));if(n.size=e,n.maintainHigh=r,n.maintainLow=o,n.maintainSum=l,n.totalPushed=0,n.periodHigh=0,n.periodLow=Infinity,n.periodSum=0,!e||"number"!=typeof e)throw"Size required and should be a number.";return n._push=n.push,n.push=function(e){this.add(e),this.totalPushed++},n}return $(t,e),Z(t,[{key:"add",value:function(e){this.length===this.size?(this.lastShift=this.shift(),this._push(e),this.maintainHigh&&this.lastShift==this.periodHigh&&this.calculatePeriodHigh(),this.maintainLow&&this.lastShift==this.periodLow&&this.calculatePeriodLow(),this.maintainSum&&(this.periodSum-=this.lastShift)):this._push(e),this.maintainHigh&&this.periodHigh<=e&&(this.periodHigh=e),this.maintainLow&&this.periodLow>=e&&(this.periodLow=e),this.maintainSum&&(this.periodSum+=e)}},{key:"iterator",value:regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:this.resetCursor();case 1:if(!this.next()){e.next=6;break}return e.next=4,this.current;case 4:e.next=1;break;case 6:case"end":return e.stop();}},e,this)})},{key:"calculatePeriodHigh",value:function(){for(this.resetCursor(),this.next()&&(this.periodHigh=this.current);this.next();)this.periodHigh<=this.current&&(this.periodHigh=this.current)}},{key:"calculatePeriodLow",value:function(){for(this.resetCursor(),this.next()&&(this.periodLow=this.current);this.next();)this.periodLow>=this.current&&(this.periodLow=this.current)}}]),t}(re),le=function e(){Y(this,e),this.open=[],this.high=[],this.low=[],this.close=[],this.volume=[],this.timestamp=[]},ne={},ae=function e(){Y(this,e)},se=function(){function e(t){Y(this,e),this.format=t.format||r}return Z(e,[{key:"getResult",value:function(){return this.result}}],[{key:"reverseInputs",value:function(e){e.reversedInput&&(e.values?e.values.reverse():void 0,e.open?e.open.reverse():void 0,e.high?e.high.reverse():void 0,e.low?e.low.reverse():void 0,e.close?e.close.reverse():void 0,e.volume?e.volume.reverse():void 0,e.timestamp?e.timestamp.reverse():void 0)}}]),e}(),ue=function(e){function t(e,r){Y(this,t);var o=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return o.period=e,o.values=r,o}return $(t,e),t}(ae),pe=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));r.period=e.period,r.price=e.values;var o=regeneratorRuntime.mark(function e(t){var r,o,l,n,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=new re,o=0,l=1,void(e.next=5);case 5:n=e.sent,r.push(0);case 7:return l<t?(l++,r.push(n),o+=n):(o=o-r.shift()+n,a=o/t,r.push(n)),e.next=11,a;case 11:n=e.sent,e.next=7;break;case 14:case"end":return e.stop();}},e,this)});return r.generator=o(r.period),r.generator.next(),r.result=[],r.price.forEach(function(e){var t=r.generator.next(e);void 0!==t.value&&r.result.push(r.format(t.value))}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;if(void 0!=t)return this.format(t)}}]),t}(se);pe.calculate=o;var ie=function(e){function t(e){Y(this,t);var r,o=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),l=e.period,n=e.values;o.result=[],r=new pe({period:l,values:[]});var a=regeneratorRuntime.mark(function e(){var t,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;case 3:if(void 0==o||void 0===t){e.next=11;break}return o=(t-o)*(2/(l+1))+o,e.next=8,o;case 8:t=e.sent,e.next=19;break;case 11:return void(e.next=13);case 13:if(t=e.sent,o=r.nextValue(t),!o){e.next=19;break}return e.next=18,o;case 18:t=e.sent;case 19:e.next=3;break;case 21:case"end":return e.stop();}},e,this)});return o.generator=a(),o.generator.next(),o.generator.next(),n.forEach(function(e){var t=o.generator.next(e);void 0!=t.value&&o.result.push(o.format(t.value))}),o}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;if(void 0!=t)return this.format(t)}}]),t}(se);ie.calculate=l;var he=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.period,l=e.values;return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,l,n,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=new re,r=o*(o+1)/2;case 2:if(!(t.length<o)){e.next=11;break}return e.t0=t,void(e.next=7);case 7:e.t1=e.sent,e.t0.push.call(e.t0,e.t1),e.next=19;break;case 11:for(t.resetCursor(),l=0,n=1;n<=o;n++)l+=t.next()*n/r;return e.next=16,l;case 16:a=e.sent,t.shift(),t.push(a);case 19:e.next=2;break;case 21:case"end":return e.stop();}},e,this)})(),r.generator.next(),l.forEach(function(e){var t=r.generator.next(e);void 0!=t.value&&r.result.push(r.format(t.value))}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;if(void 0!=t)return this.format(t)}}]),t}(se);he.calculate=n;var ge=function(e){function t(e){Y(this,t);var r,o=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),l=e.period,n=e.values;o.result=[],r=new pe({period:l,values:[]});var a=regeneratorRuntime.mark(function e(){var t,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;case 3:if(void 0==o||void 0===t){e.next=11;break}return o=(t-o)*(1/l)+o,e.next=8,o;case 8:t=e.sent,e.next=19;break;case 11:return void(e.next=13);case 13:if(t=e.sent,o=r.nextValue(t),void 0===o){e.next=19;break}return e.next=18,o;case 18:t=e.sent;case 19:e.next=3;break;case 21:case"end":return e.stop();}},e,this)});return o.generator=a(),o.generator.next(),o.generator.next(),n.forEach(function(e){var t=o.generator.next(e);void 0!=t.value&&o.result.push(o.format(t.value))}),o}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;if(void 0!=t)return this.format(t)}}]),t}(se);ge.calculate=a;var ve=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return r.values=e,r.SimpleMAOscillator=!0,r.SimpleMASignal=!0,r}return $(t,e),t}(ae),ce=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.SimpleMAOscillator?pe:ie,l=e.SimpleMASignal?pe:ie,n=new o({period:e.fastPeriod,values:[],format:function(e){return e}}),a=new o({period:e.slowPeriod,values:[],format:function(e){return e}}),s=new l({period:e.signalPeriod,values:[],format:function(e){return e}}),u=r.format;return r.result=[],r.generator=regeneratorRuntime.mark(function t(){var r,o,l,p,i,h,g;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:r=0;case 1:if(!(r<e.slowPeriod)){t.next=10;break}return void(t.next=5);case 5:return o=t.sent,h=n.nextValue(o),g=a.nextValue(o),r++,t.abrupt("continue",1);case 10:return h&&g&&(l=h-g,p=s.nextValue(l)),i=l-p,t.next=14,{MACD:u(l),signal:p?u(p):void 0,histogram:isNaN(i)?void 0:u(i)};case 14:o=t.sent,h=n.nextValue(o),g=a.nextValue(o),t.next=1;break;case 19:case"end":return t.stop();}},t,this)})(),r.generator.next(),e.values.forEach(function(e){var t=r.generator.next(e);void 0!=t.value&&r.result.push(t.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;return t}}]),t}(se);ce.calculate=s;var _e=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),de=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.values,l=e.period,n=r.format;return r.generator=regeneratorRuntime.mark(function e(t){var r,o,l,a,s,u;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:return r=e.sent,o=1,l=0,u=r,void(e.next=8);case 8:r=e.sent;case 9:return s=r-u,s=0<s?s:0,0<s&&(l+=s),o<t?o++:void 0==a?a=l/t:a=(a*(t-1)+s)/t,u=r,a=void 0===a?void 0:n(a),e.next=18,a;case 18:r=e.sent,e.next=9;break;case 21:case"end":return e.stop();}},e,this)})(l),r.generator.next(),r.result=[],o.forEach(function(e){var t=r.generator.next(e);void 0!==t.value&&r.result.push(t.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){return this.generator.next(e).value}}]),t}(se);de.calculate=u;var xe=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),me=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.values,l=e.period,n=r.format;return r.generator=regeneratorRuntime.mark(function e(t){var r,o,l,a,s,u;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:return r=e.sent,o=1,l=0,u=r,void(e.next=8);case 8:r=e.sent;case 9:return s=u-r,s=0<s?s:0,0<s&&(l+=s),o<t?o++:void 0==a?a=l/t:a=(a*(t-1)+s)/t,u=r,a=void 0===a?void 0:n(a),e.next=18,a;case 18:r=e.sent,e.next=9;break;case 21:case"end":return e.stop();}},e,this)})(l),r.generator.next(),r.result=[],o.forEach(function(e){var t=r.generator.next(e);void 0!==t.value&&r.result.push(t.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){return this.generator.next(e).value}}]),t}(se);me.calculate=p;var fe=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),we=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.period,l=e.values,n=new de({period:o,values:[]}),a=new me({period:o,values:[]}),s=1;return r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,u;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;case 3:return r=n.nextValue(t),o=a.nextValue(t),void 0!==r&&void 0!==o&&(0===o?u=100:0===r?u=0:(l=r/o,l=isNaN(l)?0:l,u=parseFloat((100-100/(1+l)).toFixed(2)))),s++,e.next=10,u;case 10:t=e.sent,e.next=3;break;case 13:case"end":return e.stop();}},e,this)})(o),r.generator.next(),r.result=[],l.forEach(function(e){var t=r.generator.next(e);void 0!==t.value&&r.result.push(t.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){return this.generator.next(e).value}}]),t}(se);we.calculate=i;var ye=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Pe=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.period,l=e.values,n=new pe({period:o,values:[],format:function(e){return e}});return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,l,a,s,u,p,i,h,g,v;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:l=new oe(o);;return void(e.next=4);case 4:t=e.sent;case 5:if(l.push(t),r=n.nextValue(t),!r){e.next=30;break}for(s=0,u=!0,p=!1,i=void 0,e.prev=13,h=l.iterator()[Symbol.iterator]();!(u=(g=h.next()).done);u=!0)v=g.value,s+=Math.pow(v-r,2);e.next=21;break;case 17:e.prev=17,e.t0=e["catch"](13),p=!0,i=e.t0;case 21:e.prev=21,e.prev=22,!u&&h.return&&h.return();case 24:if(e.prev=24,!p){e.next=27;break}throw i;case 27:return e.finish(24);case 28:return e.finish(21);case 29:a=Math.sqrt(s/o);case 30:return e.next=32,a;case 32:t=e.sent,e.next=5;break;case 35:case"end":return e.stop();}},e,this,[[13,17,21,29],[22,,24,28]])})(),r.generator.next(),l.forEach(function(e){var t=r.generator.next(e);void 0!=t.value&&r.result.push(r.format(t.value))}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!=t.value)return this.format(t.value)}}]),t}(se);Pe.calculate=h;var ke=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Ie=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Oe=function(e){function t(e){Y(this,t);var r,o,l=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),n=e.period,a=e.values,s=e.stdDev,u=l.format;return l.result=[],r=new pe({period:n,values:[],format:function(e){return e}}),o=new Pe({period:n,values:[],format:function(e){return e}}),l.generator=regeneratorRuntime.mark(function e(){var t,l,n,a,p,i,h,g;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:l=e.sent;case 3:return n=r.nextValue(l),a=o.nextValue(l),n&&(p=u(n),i=u(n+a*s),h=u(n-a*s),g=u((l-h)/(i-h)),t={middle:p,upper:i,lower:h,pb:g}),e.next=9,t;case 9:l=e.sent,e.next=3;break;case 12:case"end":return e.stop();}},e,this)})(),l.generator.next(),a.forEach(function(e){var t=l.generator.next(e);void 0!=t.value&&l.result.push(t.value)}),l}return $(t,e),Z(t,[{key:"nextValue",value:function(e){return this.generator.next(e).value}}]),t}(se);Oe.calculate=g;var Ve=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));r.period=e.period,r.price=e.values;var o=regeneratorRuntime.mark(function e(t){var r,o,l,n,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=new re,o=0,l=1,void(e.next=5);case 5:n=e.sent,a=0;case 7:return l<t?(l++,o+=n,a=void 0):l==t?(l++,o+=n,a=o):a=a-a/t+n,e.next=11,a;case 11:n=e.sent,e.next=7;break;case 14:case"end":return e.stop();}},e,this)});return r.generator=o(r.period),r.generator.next(),r.result=[],r.price.forEach(function(e){var t=r.generator.next(e);void 0!=t.value&&r.result.push(r.format(t.value))}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;if(void 0!=t)return this.format(t)}}]),t}(se);Ve.calculate=function(e){se.reverseInputs(e);var t=new Ve(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t};var be=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Ce=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.low,l=e.high,n=r.format;if(o.length!=l.length)throw"Inputs(low,high) not of equal size";return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:r=e.sent;case 3:return o&&(l=r.high-o.high,a=o.low-r.low,t=n(a>l&&0<a?a:0)),o=r,e.next=8,t;case 8:r=e.sent,e.next=3;break;case 11:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var n=r.generator.next({high:l[t],low:o[t]});void 0!==n.value&&r.result.push(n.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){return this.generator.next(e).value}}],[{key:"calculate",value:function(e){se.reverseInputs(e);var r=new t(e).result;return e.reversedInput&&r.reverse(),se.reverseInputs(e),r}}]),t}(se),qe=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Ee=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.low,l=e.high,n=r.format;if(o.length!=l.length)throw"Inputs(low,high) not of equal size";return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:r=e.sent;case 3:return o&&(l=r.high-o.high,a=o.low-r.low,t=n(l>a&&0<l?l:0)),o=r,e.next=8,t;case 8:r=e.sent,e.next=3;break;case 11:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var n=r.generator.next({high:l[t],low:o[t]});void 0!==n.value&&r.result.push(n.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){return this.generator.next(e).value}}],[{key:"calculate",value:function(e){se.reverseInputs(e);var r=new t(e).result;return e.reversedInput&&r.reverse(),se.reverseInputs(e),r}}]),t}(se),Se=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Ae=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.low,l=e.high,n=e.close,a=r.format;if(o.length!=l.length)throw"Inputs(low,high) not of equal size";return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;case 3:if(void 0!=r){e.next=9;break}return r=t.close,e.next=8,o;case 8:t=e.sent;case 9:return o=X(t.high-t.low,isNaN(Q(t.high-r))?0:Q(t.high-r),isNaN(Q(t.low-r))?0:Q(t.low-r)),r=t.close,void 0!=o&&(o=a(o)),e.next=14,o;case 14:t=e.sent,e.next=3;break;case 17:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var a=r.generator.next({high:l[t],low:o[t],close:n[t]});void 0!=a.value&&r.result.push(a.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){return this.generator.next(e).value}}]),t}(se);Ae.calculate=v;var Be=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),He=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Le=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.low,l=e.high,n=e.close,a=e.period,s=r.format,u=new Ee({high:[],low:[]}),p=new Ce({high:[],low:[]}),i=new Ve({period:a,values:[],format:function(e){return e}}),h=new Ve({period:a,values:[],format:function(e){return e}}),g=new Ve({period:a,values:[],format:function(e){return e}}),v=new ge({period:a,values:[],format:function(e){return e}}),c=new Ae({low:[],high:[],close:[]});if(o.length!==l.length||l.length!==n.length)throw"Inputs(low,high, close) not of equal size";return r.result=[],He,r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,n,a,s,_,d,x,m,f,w,y,P,k,I;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent,r=0,o=0,l=0,n=0;case 7:if(x=c.nextValue(t),m=u.nextValue(t),f=p.nextValue(t),void 0!==x){e.next=16;break}return void(e.next=14);case 14:return t=e.sent,e.abrupt("continue",7);case 16:return w=g.nextValue(x),y=i.nextValue(m),P=h.nextValue(f),void 0!=w&&void 0!=y&&void 0!=P&&(a=100*y/w,s=100*P/w,k=Q(a-s),I=a+s,_=100*(k/I),d=v.nextValue(_)),e.next=22,{adx:d,pdi:a,mdi:s};case 22:t=e.sent,e.next=7;break;case 25:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var a=r.generator.next({high:l[t],low:o[t],close:n[t]});void 0!=a.value&&void 0!=a.value.adx&&r.result.push({adx:s(a.value.adx),pdi:s(a.value.pdi),mdi:s(a.value.mdi)})}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;if(void 0!=t&&void 0!=t.adx)return{adx:this.format(t.adx),pdi:this.format(t.pdi),mdi:this.format(t.mdi)}}}]),t}(se);Le.calculate=c;var ze=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Te=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.low,l=e.high,n=e.close,a=e.period,s=r.format;if(o.length!==l.length||l.length!==n.length)throw"Inputs(low,high, close) not of equal size";var u=new Ae({low:[],high:[],close:[]}),p=new ge({period:a,values:[],format:function(e){return e}});return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;;case 4:return o=u.nextValue({low:t.low,high:t.high,close:t.close}),r=void 0===o?void 0:p.nextValue(o),e.next=9,r;case 9:t=e.sent,e.next=4;break;case 12:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var a=r.generator.next({high:l[t],low:o[t],close:n[t]});void 0!==a.value&&r.result.push(s(a.value))}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){return this.generator.next(e).value}}]),t}(se);Te.calculate=_;var De=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Me=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.period,l=e.values;return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,l,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=1,r=new oe(o);;return void(e.next=5);case 5:l=e.sent;case 6:return r.push(l),t<o?t++:n=100*((l-r.lastShift)/r.lastShift),e.next=11,n;case 11:l=e.sent,e.next=6;break;case 14:case"end":return e.stop();}},e,this)})(),r.generator.next(),l.forEach(function(e){var t=r.generator.next(e);void 0==t.value||isNaN(t.value)||r.result.push(r.format(t.value))}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!=t.value&&!isNaN(t.value))return this.format(t.value)}}]),t}(se);Me.calculate=d;var Re=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),je=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.values,l=e.ROCPer1,n=e.ROCPer2,a=e.ROCPer3,s=e.ROCPer4,u=e.SMAROCPer1,p=e.SMAROCPer2,i=e.SMAROCPer3,h=e.SMAROCPer4,g=e.signalPeriod,v=new Me({period:l,values:[]}),c=new Me({period:n,values:[]}),_=new Me({period:a,values:[]}),d=new Me({period:s,values:[]}),x=new pe({period:u,values:[],format:function(e){return e}}),m=new pe({period:p,values:[],format:function(e){return e}}),f=new pe({period:i,values:[],format:function(e){return e}}),w=new pe({period:h,values:[],format:function(e){return e}}),y=new pe({period:g,values:[],format:function(e){return e}}),P=r.format;r.result=[];var k=X(l+u,n+p,a+i,s+h);return r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,n,a,s,u,p,i,h,g,I;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=1,void(e.next=3);case 3:r=e.sent,o=void 0,l=void 0,n=void 0,a=void 0,s=void 0,u=void 0,p=void 0;case 6:return i=v.nextValue(r),h=c.nextValue(r),g=_.nextValue(r),I=d.nextValue(r),l=void 0===i?void 0:x.nextValue(i),n=void 0===h?void 0:m.nextValue(h),a=void 0===g?void 0:f.nextValue(g),s=void 0===I?void 0:w.nextValue(I),t<k?t++:o=1*l+2*n+3*a+4*s,u=void 0===o?void 0:y.nextValue(o),p=void 0===o?void 0:{kst:P(o),signal:u?P(u):void 0},e.next=20,p;case 20:r=e.sent,e.next=6;break;case 23:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e){var t=r.generator.next(e);void 0!=t.value&&r.result.push(t.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!=t.value)return t.value}}]),t}(se);je.calculate=x;var Fe=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),We=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.high||[],l=e.low||[],n=regeneratorRuntime.mark(function e(t,r){var o,l,n,a,s,u,p;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return o=void 0,l=void 0,n=void 0,a=void 0,s=!0,u=t,void(e.next=5);case 5:p=e.sent;case 6:return o?(n+=u*(l-n),s?(n=N(n,a.low,p.low),o.high>l&&(l=o.high,u=N(u+t,r))):(n=X(n,a.high,p.high),o.low<l&&(l=o.low,u=N(u+t,r))),(s&&o.low<n||!s&&o.high>n)&&(u=t,n=l,s=!s,l=s?o.high:o.low)):(n=p.low,l=p.high),a=p,o&&(p=o),e.next=12,n;case 12:o=e.sent,e.next=6;break;case 15:case"end":return e.stop();}},e,this)});return r.result=[],r.generator=n(e.step,e.max),r.generator.next(),l.forEach(function(e,t){var n=r.generator.next({high:o[t],low:l[t]});void 0!==n.value&&r.result.push(n.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!==t.value)return t.value}}]),t}(se);We.calculate=m;var Ue=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Ke=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.low,l=e.high,n=e.close,a=e.period,s=e.signalPeriod,u=r.format;if(o.length!==l.length||l.length!==n.length)throw"Inputs(low,high, close) not of equal size";return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,n,p,i,h;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=1,r=new oe(a,!0,!1),o=new oe(a,!1,!0),l=new pe({period:s,values:[],format:function(e){return e}}),n=void 0,p=void 0,void(e.next=7);case 7:i=e.sent;case 8:if(r.push(i.high),o.push(i.low),!(t<a)){e.next=17;break}return t++,void(e.next=15);case 15:return i=e.sent,e.abrupt("continue",8);case 17:return h=o.periodLow,n=100*((i.close-h)/(r.periodHigh-h)),n=isNaN(n)?0:n,p=l.nextValue(n),e.next=23,{k:u(n),d:void 0===p?void 0:u(p)};case 23:i=e.sent,e.next=8;break;case 26:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var a=r.generator.next({high:l[t],low:o[t],close:n[t]});void 0!==a.value&&r.result.push(a.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!==t.value)return t.value}}]),t}(se);Ke.calculate=f;var Ge=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Ne=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.low,l=e.high,n=e.close,a=e.period,s=r.format;if(o.length!==l.length||l.length!==n.length)throw"Inputs(low,high, close) not of equal size";return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,n,u,p;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=1,r=new oe(a,!0,!1),o=new oe(a,!1,!0),l=void 0,n=void 0,void(e.next=7);case 7:u=e.sent,p=void 0;case 9:if(r.push(u.high),o.push(u.low),!(t<a)){e.next=18;break}return t++,void(e.next=16);case 16:return u=e.sent,e.abrupt("continue",9);case 18:return l=o.periodLow,n=r.periodHigh,p=s(-100*((n-u.close)/(n-l))),e.next=23,p;case 23:u=e.sent,e.next=9;break;case 26:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var a=r.generator.next({high:l[t],low:o[t],close:n[t]});void 0!==a.value&&r.result.push(a.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!=t.value)return this.format(t.value)}}]),t}(se);Ne.calculate=w;var Xe=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Qe=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.high,l=e.low,n=e.close,a=e.volume;if(l.length!==o.length||o.length!==n.length||o.length!==a.length)throw"Inputs(low,high, close, volumes) not of equal size";return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=0,void(e.next=3);case 3:r=e.sent;case 4:return o=(r.close-r.low-(r.high-r.close))/(r.high-r.low),o=isNaN(o)?1:o,l=o*r.volume,t+=l,e.next=11,Math.round(t);case 11:r=e.sent,e.next=4;break;case 14:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var o={high:e,low:l[t],close:n[t],volume:a[t]},s=r.generator.next(o);void 0!=s.value&&r.result.push(s.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){return this.generator.next(e).value}}]),t}(se);Qe.calculate=y;var Ye=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Ze=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.close,l=e.volume;return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=0,void(e.next=3);case 3:if(r=e.sent,!(r.close&&"number"==typeof r.close)){e.next=9;break}return o=r.close,void(e.next=8);case 8:r=e.sent;case 9:return o<r.close?t+=r.volume:r.close<o&&(t-=r.volume),o=r.close,e.next=14,t;case 14:r=e.sent,e.next=9;break;case 17:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var n={close:o[t],volume:l[t]},a=r.generator.next(n);void 0!=a.value&&r.result.push(a.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){return this.generator.next(e).value}}]),t}(se);Ze.calculate=P;var $e=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Je=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.values,l=e.period,n=r.format,a=new ie({period:l,values:[],format:function(e){return e}}),s=new ie({period:l,values:[],format:function(e){return e}}),u=new ie({period:l,values:[],format:function(e){return e}}),p=new Me({period:1,values:[],format:function(e){return e}});return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,i;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;case 3:return r=a.nextValue(t),o=r?s.nextValue(r):void 0,l=o?u.nextValue(o):void 0,i=l?p.nextValue(l):void 0,e.next=10,i?n(i):void 0;case 10:t=e.sent,e.next=3;break;case 13:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e){var t=r.generator.next(e);void 0!==t.value&&r.result.push(t.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!==t.value)return t.value}}]),t}(se);Je.calculate=k;var et=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments));return e.period=1,e}return $(t,e),t}(ae),tt=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.close,l=e.volume,n=e.period||1;if(l.length!==o.length)throw"Inputs(volume, close) not of equal size";var a=new ie({values:[],period:n});return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:return t=e.sent,void(e.next=5);case 5:r=e.sent,o=void 0;case 7:return o=(r.close-t.close)*r.volume,t=r,e.next=12,a.nextValue(o);case 12:r=e.sent,e.next=7;break;case 15:case"end":return e.stop();}},e,this)})(),r.generator.next(),l.forEach(function(e,t){var n=r.generator.next({close:o[t],volume:l[t]});void 0!=n.value&&r.result.push(n.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;if(void 0!=t)return t}}]),t}(se);tt.calculate=I;var rt=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),ot=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.low,l=e.high,n=e.close,a=e.period,s=r.format,u=new oe(a),p=new pe({period:a,values:[],format:function(e){return e}});if(o.length!==l.length||l.length!==n.length)throw"Inputs(low,high, close) not of equal size";return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,n,s,i,h,g,v,c,_;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;case 3:if(r=(t.high+t.low+t.close)/3,u.push(r),o=p.nextValue(r),l=null,n=void 0,s=0,void 0==o){e.next=32;break}for(i=!0,h=!1,g=void 0,e.prev=14,v=u.iterator()[Symbol.iterator]();!(i=(c=v.next()).done);i=!0)_=c.value,s+=Q(_-o);e.next=22;break;case 18:e.prev=18,e.t0=e["catch"](14),h=!0,g=e.t0;case 22:e.prev=22,e.prev=23,!i&&v.return&&v.return();case 25:if(e.prev=25,!h){e.next=28;break}throw g;case 28:return e.finish(25);case 29:return e.finish(22);case 30:l=s/a,n=(r-o)/(.015*l);case 32:return e.next=34,n;case 34:t=e.sent,e.next=3;break;case 37:case"end":return e.stop();}},e,this,[[14,18,22,30],[23,,25,29]])})(),r.generator.next(),o.forEach(function(e,t){var a=r.generator.next({high:l[t],low:o[t],close:n[t]});void 0!=a.value&&r.result.push(a.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;if(void 0!=t)return t}}]),t}(se);ot.calculate=O;var lt=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),nt=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.high,l=e.low,n=e.fastPeriod,a=e.slowPeriod,s=new pe({values:[],period:a}),u=new pe({values:[],period:n});return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:r=e.sent;case 3:return o=(r.high+r.low)/2,l=s.nextValue(o),n=u.nextValue(o),void 0!==l&&void 0!==n&&(t=n-l),e.next=10,t;case 10:r=e.sent,e.next=3;break;case 13:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var o={high:e,low:l[t]},n=r.generator.next(o);void 0!=n.value&&r.result.push(r.format(n.value))}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!=t.value)return this.format(t.value)}}]),t}(se);nt.calculate=V;var at=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),st=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.low,l=e.high,n=e.close,a=e.volume,s=r.format;if(o.length!==l.length||l.length!==n.length)throw"Inputs(low,high, close) not of equal size";return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent,r=0,o=0;case 5:return l=(t.high+t.low+t.close)/3,n=t.volume*l,r+=n,o+=t.volume,e.next=12,r/o;case 12:t=e.sent;;e.next=5;break;case 16:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var s=r.generator.next({high:l[t],low:o[t],close:n[t],volume:a[t]});void 0!=s.value&&r.result.push(s.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;if(void 0!=t)return t}}]),t}(se);st.calculate=b;var ut=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),pt=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.high,l=e.low,n=e.close,a=e.open,s=e.volume,u=e.noOfBars;if(l.length!==o.length||o.length!==n.length||o.length!==s.length)throw"Inputs(low,high, close, volumes) not of equal size";r.result=[];for(var p=X.apply(Math,ee(o).concat(ee(l),ee(n),ee(a))),h=N.apply(Math,ee(o).concat(ee(l),ee(n),ee(a))),g=h,v=0;v<u;v++){var i=g,c=i+(p-h)/u;g=c;for(var _=0,d=0,x=0,m=0;m<o.length;m++){var f=l[m],w=o[m],y=a[m],P=n[m],k=s[m];C(i,c,f,w)&&(x+=k,y>P?d+=k:_+=k)}r.result.push({rangeStart:i,rangeEnd:c,bullishVolume:_,bearishVolume:d,totalVolume:x})}return r}return $(t,e),Z(t,[{key:"nextValue",value:function(){throw"Next value not supported for volume profile"}}]),t}(se);pt.calculate=q;var it=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),ht=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;case 3:return e.next=6,(t.high+t.low+t.close)/3;case 6:t=e.sent,e.next=3;break;case 9:case"end":return e.stop();}},e,this)})(),r.generator.next(),e.low.forEach(function(t,o){var l=r.generator.next({high:e.high[o],low:e.low[o],close:e.close[o]});r.result.push(l.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;return t}}]),t}(se);ht.calculate=function(e){se.reverseInputs(e);var t=new ht(e).result;return e.reversedInput&&t.reverse(),se.reverseInputs(e),t};var gt=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),vt=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.high,l=e.low,n=e.close,a=e.volume,s=e.period,u=new ht({low:[],high:[],close:[]}),p=new oe(s,!1,!1,!0),i=new oe(s,!1,!1,!0);if(l.length!==o.length||o.length!==n.length||o.length!==a.length)throw"Inputs(low,high, close, volumes) not of equal size";return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,n,a,h,g,v,c,_,d,x,m,f,w;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=0,g=null,v=null,void(e.next=5);case 5:return r=e.sent,o=r.close,void(e.next=9);case 9:r=e.sent;case 10:return c=r,_=c.high,d=c.low,x=c.close,m=c.volume,f=0,w=0,g=u.nextValue({high:_,low:d,close:x}),n=g*m,null!=g&&null!=v&&(g>v?f=n:w=n,p.push(f),i.push(w),l=p.periodSum,h=i.periodSum,p.totalPushed>=s&&p.totalPushed>=s&&(a=l/h,t=100-100/(1+a))),v=g,e.next=20,t;case 20:r=e.sent,e.next=10;break;case 23:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e,t){var o={high:e,low:l[t],close:n[t],volume:a[t]},s=r.generator.next(o);void 0!=s.value&&r.result.push(parseFloat(s.value.toFixed(2)))}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!=t.value)return parseFloat(t.value.toFixed(2))}}]),t}(se);vt.calculate=E;var ct=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),_t=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.values,l=e.rsiPeriod,n=e.stochasticPeriod,a=e.kPeriod,s=e.dPeriod,u=r.format;return r.result=[],r.generator=regeneratorRuntime.mark(function e(){var t,r,o,u,p,i,h,g,v,c;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=1,r=new we({period:l,values:[]}),o=new Ke({period:n,high:[],low:[],close:[],signalPeriod:a}),u=new pe({period:s,values:[],format:function(e){return e}}),p=void 0,i=void 0,h=void 0,g=void 0,void(e.next=7);case 7:v=e.sent;case 8:return p=r.nextValue(v),void 0!==p&&(c={high:p,low:p,close:p},i=o.nextValue(c),void 0!==i&&void 0!==i.d&&(h=u.nextValue(i.d),void 0!==h&&(g={stochRSI:i.k,k:i.d,d:h}))),e.next=13,g;case 13:v=e.sent,e.next=8;break;case 16:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e){var t=r.generator.next(e);void 0!==t.value&&r.result.push(t.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!==t.value)return t.value}}]),t}(se);_t.calculate=S;var dt=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),xt=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.values,l=e.period;r.result=[];var n=new oe(l,!0,!1,!1);return r.generator=regeneratorRuntime.mark(function e(){var t,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;case 3:return n.push(t),n.totalPushed>=l&&(r=n.periodHigh),e.next=8,r;case 8:t=e.sent,e.next=3;break;case 11:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e){var t=r.generator.next(e);void 0!=t.value&&r.result.push(t.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!=t.value)return t.value}}]),t}(se);xt.calculate=A;var mt=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),ft=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.values,l=e.period;r.result=[];var n=new oe(l,!1,!0,!1);return r.generator=regeneratorRuntime.mark(function e(){var t,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;case 3:return n.push(t),n.totalPushed>=l&&(r=n.periodLow),e.next=8,r;case 8:t=e.sent,e.next=3;break;case 11:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e){var t=r.generator.next(e);void 0!=t.value&&r.result.push(t.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!=t.value)return t.value}}]),t}(se);ft.calculate=B;var wt=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),yt=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.values,l=e.period;r.result=[];var n=new oe(l,!1,!1,!0);return r.generator=regeneratorRuntime.mark(function e(){var t,r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;case 3:return n.push(t),n.totalPushed>=l&&(r=n.periodSum),e.next=8,r;case 8:t=e.sent,e.next=3;break;case 11:case"end":return e.stop();}},e,this)})(),r.generator.next(),o.forEach(function(e){var t=r.generator.next(e);void 0!=t.value&&r.result.push(t.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!=t.value)return t.value}}]),t}(se);yt.calculate=H;var Pt=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),kt=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=r.format,l=e.useATR,n=e.brickSize||0;if(l){var a=_(Object.assign({},e));n=a[a.length-1]}if(r.result=new le,0===n)return console.error("Not enough data to calculate brickSize for renko when using ATR"),J(r);var s=0,u=0,p=Infinity,i=0,h=0,g=0;return r.generator=regeneratorRuntime.mark(function e(){var t,r,o,l,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent;case 3:if(0!=s){e.next=15;break}return s=t.close,u=t.high,p=t.low,i=t.close,h=t.volume,g=t.timestamp,void(e.next=13);case 13:return t=e.sent,e.abrupt("continue",3);case 15:if(r=Q(t.close-i),o=Q(t.close-s),!(r>=n&&o>=n)){e.next=30;break}return l=r>o?s:i,a={open:l,high:u>t.high?u:t.high,low:p<t.Low?p:t.low,close:l>t.close?l-n:l+n,volume:h+t.volume,timestamp:t.timestamp},s=a.open,u=a.close,p=a.close,i=a.close,h=0,e.next=27,a;case 27:t=e.sent,e.next=37;break;case 30:return u=u>t.high?u:t.high,p=p<t.Low?p:t.low,h+=t.volume,g=t.timestamp,void(e.next=36);case 36:t=e.sent;case 37:e.next=3;break;case 39:case"end":return e.stop();}},e,this)})(),r.generator.next(),e.low.forEach(function(t,o){var l=r.generator.next({open:e.open[o],high:e.high[o],low:e.low[o],close:e.close[o],volume:e.volume[o],timestamp:e.timestamp[o]});l.value&&(r.result.open.push(l.value.open),r.result.high.push(l.value.high),r.result.low.push(l.value.low),r.result.close.push(l.value.close),r.result.volume.push(l.value.volume),r.result.timestamp.push(l.value.timestamp))}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(){return console.error("Cannot calculate next value on Renko, Every value has to be recomputed for every change, use calcualte method"),null}}]),t}(se);kt.calculate=L;var It=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),Ot=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=r.format;r.result=new le;var l=null,n=0,a=Infinity,s=0,u=0,p=0;return r.generator=regeneratorRuntime.mark(function e(){var t,r,o,i,h,g;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent,r=null;case 4:return null==l?(l=(t.close+t.open)/2,n=t.high,a=t.low,s=(t.close+t.open+t.high+t.low)/4,u=t.volume||0,p=t.timestamp||0,r={open:l,high:n,low:a,close:s,volume:t.volume||0,timestamp:t.timestamp||0}):(o=(t.close+t.open+t.high+t.low)/4,i=(l+s)/2,h=X(i,o,t.high),g=N(t.low,i,o),r={close:o,open:i,high:h,low:g,volume:t.volume||0,timestamp:t.timestamp||0},s=o,l=i,n=h,a=g),e.next=8,r;case 8:t=e.sent,e.next=4;break;case 11:case"end":return e.stop();}},e,this)})(),r.generator.next(),e.low.forEach(function(t,o){var l=r.generator.next({open:e.open[o],high:e.high[o],low:e.low[o],close:e.close[o],volume:e.volume?e.volume[o]:e.volume,timestamp:e.timestamp?e.timestamp[o]:e.timestamp});l.value&&(r.result.open.push(l.value.open),r.result.high.push(l.value.high),r.result.low.push(l.value.low),r.result.close.push(l.value.close),r.result.volume.push(l.value.volume),r.result.timestamp.push(l.value.timestamp))}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e).value;return t}}]),t}(se);Ot.calculate=z;var Vt=function(){function e(){Y(this,e)}return Z(e,[{key:"approximateEqual",value:function(e,t){var r=1*parseFloat(Q(e-t).toPrecision(4)),o=1*parseFloat((1e-3*e).toPrecision(4));return r<=o}},{key:"logic",value:function(){throw"this has to be implemented"}},{key:"getAllPatternIndex",value:function(e){var t=this;if(e.close.length<this.requiredCount)return console.warn("Data count less than data required for the strategy ",this.name),[];e.reversedInput&&(e.open.reverse(),e.high.reverse(),e.low.reverse(),e.close.reverse());var r=this.logic;return this._generateDataForCandleStick(e).map(function(e,o){return r.call(t,e)?o:void 0}).filter(function(e){return e})}},{key:"hasPattern",value:function(e){if(e.close.length<this.requiredCount)return console.warn("Data count less than data required for the strategy ",this.name),!1;e.reversedInput&&(e.open.reverse(),e.high.reverse(),e.low.reverse(),e.close.reverse());var t=this.logic;return t.call(this,this._getLastDataForCandleStick(e))}},{key:"_getLastDataForCandleStick",value:function(e){var t=this.requiredCount;if(e.close.length===t)return e;for(var r={open:[],high:[],low:[],close:[]},o=0,l=e.close.length-t;o<t;)r.open.push(e.open[l+o]),r.high.push(e.high[l+o]),r.low.push(e.low[l+o]),r.close.push(e.close[l+o]),o++;return r}},{key:"_generateDataForCandleStick",value:function(e){var t=this.requiredCount,r=e.close.map(function(r,o){for(var l=0,n={open:[],high:[],low:[],close:[]};l<t;)n.open.push(e.open[o+l]),n.high.push(e.high[o+l]),n.low.push(e.low[o+l]),n.close.push(e.close[o+l]),l++;return n}).filter(function(r,o){return o<=e.close.length-t});return r}}]),e}(),bt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="MorningStar",e.requiredCount=3,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1],p=e.open[2],i=e.close[2],h=e.high[2],g=e.low[2];return r<t&&l>u&&l>s&&s<l&&u<l&&p>s&&a<p&&p<i&&i>(t+r)/2}}]),t}(Vt),Ct=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="BullishEngulfingPattern",e.requiredCount=2,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1];return r<t&&t>n&&r>n&&t<a}}]),t}(Vt),qt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.requiredCount=2,e.name="BullishHarami",e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1];return t>n&&r<n&&r<a&&t>u&&o>s}}]),t}(Vt),Et=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.requiredCount=2,e.name="BullishHaramiCross",e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1],p=this.approximateEqual(n,a);return t>n&&r<n&&r<a&&t>u&&o>s&&p}}]),t}(Vt),St=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="Doji",e.requiredCount=1,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=this.approximateEqual(t,r),a=n&&this.approximateEqual(t,o),s=n&&this.approximateEqual(r,l);return n&&a==s}}]),t}(Vt),At=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="MorningDojiStar",e.requiredCount=3,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1],p=e.open[2],i=e.close[2],h=e.high[2],g=e.low[2],v=new St().hasPattern({open:[n],close:[a],high:[s],low:[u]});return r<t&&v&&p<i&&s<l&&u<l&&p>s&&a<p&&i>(t+r)/2}}]),t}(Vt),Bt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.requiredCount=3,e.name="DownsideTasukiGap",e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1],p=e.open[2],i=e.close[2],h=e.high[2],g=e.low[2];return r<t&&a<n&&i>p&&s<l&&n>p&&a<p&&i>n&&i<r}}]),t}(Vt),Ht=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="BullishMarubozu",e.requiredCount=1,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=this.approximateEqual(r,o)&&this.approximateEqual(l,t)&&t<r&&t<o;return n}}]),t}(Vt),Lt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.requiredCount=2,e.name="PiercingLine",e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1];return u<l&&r<t&&l>n&&a>(t+r)/2&&a>n}}]),t}(Vt),zt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="ThreeWhiteSoldiers",e.requiredCount=3,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1],p=e.open[2],i=e.close[2],h=e.high[2],g=e.low[2];return s>o&&h>s&&t<r&&n<a&&p<i&&r>n&&n<o&&s>p&&p<a}}]),t}(Vt),Tt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="BullishHammerStick",e.requiredCount=1,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=r>t;return n=n&&this.approximateEqual(r,o),n=n&&r-t<=2*(t-l),n}}]),t}(Vt),Dt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="BullishInvertedHammerStick",e.requiredCount=1,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=r>t;return n=n&&this.approximateEqual(t,l),n=n&&r-t<=2*(o-r),n}}]),t}(Vt),Mt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="BearishHammerStick",e.requiredCount=1,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=t>r;return n=n&&this.approximateEqual(t,o),n=n&&t-r<=2*(r-l),n}}]),t}(Vt),Rt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="BearishInvertedHammerStick",e.requiredCount=1,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=t>r;return n=n&&this.approximateEqual(r,l),n=n&&t-r<=2*(o-t),n}}]),t}(Vt),jt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="HammerPattern",e.requiredCount=5,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=this.downwardTrend(e);return t=t&&this.includesHammer(e),t=t&&this.hasConfirmation(e),t}},{key:"downwardTrend",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!0,r=t?3:4,o=u({values:e.close.slice(0,r),period:r-1}),l=p({values:e.close.slice(0,r),period:r-1});return l>o}},{key:"includesHammer",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!0,r=t?3:4,o=t?4:void 0,l={open:e.open.slice(r,o),close:e.close.slice(r,o),low:e.low.slice(r,o),high:e.high.slice(r,o)},n=M(l);return n=n||R(l),n=n||T(l),n=n||D(l),n}},{key:"hasConfirmation",value:function(e){var t={open:e.open[3],close:e.close[3],low:e.low[3],high:e.high[3]},r={open:e.open[4],close:e.close[4],low:e.low[4],high:e.high[4]},o=r.open<r.close;return o&&t.close<r.close}}]),t}(Vt),Ft=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="HammerPatternUnconfirmed",e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=this.downwardTrend(e,!1);return t=t&&this.includesHammer(e,!1),t}}]),t}(jt),Wt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="TweezerBottom",e.requiredCount=5,e}return $(t,e),Z(t,[{key:"logic",value:function(e){return this.downwardTrend(e)&&e.low[3]==e.low[4]}},{key:"downwardTrend",value:function(e){var t=u({values:e.close.slice(0,3),period:2}),r=p({values:e.close.slice(0,3),period:2});return r>t}}]),t}(Vt),Ut=[new Ct,new Bt,new qt,new Et,new At,new bt,new Ht,new Lt,new zt,new Tt,new Dt,new jt,new Ft,new Wt],Kt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="Bullish Candlesticks",e}return $(t,e),Z(t,[{key:"hasPattern",value:function(e){return Ut.reduce(function(t,r){var o=r.hasPattern(e);return t||o},!1)}}]),t}(Vt),Gt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="BearishEngulfingPattern",e.requiredCount=2,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1];return r>t&&t<n&&r<n&&t>a}}]),t}(Vt),Nt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.requiredCount=2,e.name="BearishHarami",e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1];return t<n&&r>n&&r>a&&t<u&&o>s}}]),t}(Vt),Xt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.requiredCount=2,e.name="BearishHaramiCross",e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1],p=this.approximateEqual(n,a);return t<n&&r>n&&r>a&&t<u&&o>s&&p}}]),t}(Vt),Qt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="EveningDojiStar",e.requiredCount=3,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1],p=e.open[2],i=e.close[2],h=e.high[2],g=e.low[2],v=new St().hasPattern({open:[n],close:[a],high:[s],low:[u]});return r>t&&v&&s>o&&u>o&&p<u&&a>p&&p>i&&i<(t+r)/2}}]),t}(Vt),Yt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="EveningStar",e.requiredCount=3,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1],p=e.open[2],i=e.close[2],h=e.high[2],g=e.low[2];return r>t&&o<u&&o<s&&s>o&&u>o&&p<u&&a>p&&p>i&&i<(t+r)/2}}]),t}(Vt),Zt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="BearishMarubozu",e.requiredCount=1,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=this.approximateEqual(t,o)&&this.approximateEqual(l,r)&&t>r&&t>l;return n}}]),t}(Vt),$t=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="ThreeBlackCrows",e.requiredCount=3,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1],p=e.open[2],i=e.close[2],h=e.high[2],g=e.low[2];return l>u&&u>g&&t>r&&n>a&&p>i&&t>n&&n>r&&n>p&&p>a}}]),t}(Vt),Jt=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="HangingMan",e.requiredCount=5,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=this.upwardTrend(e);return t=t&&this.includesHammer(e),t=t&&this.hasConfirmation(e),t}},{key:"upwardTrend",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!0,r=t?3:4,o=u({values:e.close.slice(0,r),period:r-1}),l=p({values:e.close.slice(0,r),period:r-1});return o>l}},{key:"includesHammer",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!0,r=t?3:4,o=t?4:void 0,l={open:e.open.slice(r,o),close:e.close.slice(r,o),low:e.low.slice(r,o),high:e.high.slice(r,o)},n=M(l);return n=n||T(l),n}},{key:"hasConfirmation",value:function(e){var t={open:e.open[3],close:e.close[3],low:e.low[3],high:e.high[3]},r={open:e.open[4],close:e.close[4],low:e.low[4],high:e.high[4]},o=r.open>r.close;return o&&t.close>r.close}}]),t}(Vt),er=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="HangingManUnconfirmed",e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=this.upwardTrend(e,!1);return t=t&&this.includesHammer(e,!1),t}}]),t}(Jt),tr=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="ShootingStar",e.requiredCount=5,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=this.upwardTrend(e);return t=t&&this.includesHammer(e),t=t&&this.hasConfirmation(e),t}},{key:"upwardTrend",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!0,r=t?3:4,o=u({values:e.close.slice(0,r),period:r-1}),l=p({values:e.close.slice(0,r),period:r-1});return o>l}},{key:"includesHammer",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!0,r=t?3:4,o=t?4:void 0,l={open:e.open.slice(r,o),close:e.close.slice(r,o),low:e.low.slice(r,o),high:e.high.slice(r,o)},n=R(l);return n=n||D(l),n}},{key:"hasConfirmation",value:function(e){var t={open:e.open[3],close:e.close[3],low:e.low[3],high:e.high[3]},r={open:e.open[4],close:e.close[4],low:e.low[4],high:e.high[4]},o=r.open>r.close;return o&&t.close>r.close}}]),t}(Vt),rr=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="ShootingStarUnconfirmed",e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=this.upwardTrend(e,!1);return t=t&&this.includesHammer(e,!1),t}}]),t}(tr),or=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="TweezerTop",e.requiredCount=5,e}return $(t,e),Z(t,[{key:"logic",value:function(e){return this.upwardTrend(e)&&e.high[3]==e.high[4]}},{key:"upwardTrend",value:function(e){var t=u({values:e.close.slice(0,3),period:2}),r=p({values:e.close.slice(0,3),period:2});return t>r}}]),t}(Vt),lr=[new Gt,new Nt,new Xt,new Qt,new Yt,new Zt,new $t,new Mt,new Rt,new Jt,new er,new tr,new rr,new or],nr=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="Bearish Candlesticks",e}return $(t,e),Z(t,[{key:"hasPattern",value:function(e){return lr.reduce(function(t,r){return t||r.hasPattern(e)},!1)}}]),t}(Vt),ar=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="AbandonedBaby",e.requiredCount=3,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1],p=e.open[2],i=e.close[2],h=e.high[2],g=e.low[2],v=new St().hasPattern({open:[n],close:[a],high:[s],low:[u]});return r<t&&v&&s<l&&g>s&&i>p&&h<t}}]),t}(Vt),sr=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="DarkCloudCover",e.requiredCount=2,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=e.open[1],a=e.close[1],s=e.high[1],u=e.low[1];return r>t&&a<n&&n>o&&a<(r+t)/2&&a>t}}]),t}(Vt),ur=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.requiredCount=1,e.name="DragonFlyDoji",e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=this.approximateEqual(t,r),a=n&&this.approximateEqual(t,o),s=n&&this.approximateEqual(r,l);return n&&a&&!s}}]),t}(Vt),pr=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.requiredCount=1,e.name="GraveStoneDoji",e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=this.approximateEqual(t,r),a=n&&this.approximateEqual(t,o),s=n&&this.approximateEqual(r,l);return n&&s&&!a}}]),t}(Vt),ir=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="BullishSpinningTop",e.requiredCount=1,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=Q(r-t),a=Q(o-r),s=Q(t-l);return n<a&&n<s}}]),t}(Vt),hr=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="BearishSpinningTop",e.requiredCount=1,e}return $(t,e),Z(t,[{key:"logic",value:function(e){var t=e.open[0],r=e.close[0],o=e.high[0],l=e.low[0],n=Q(r-t),a=Q(o-t),s=Q(o-l);return n<a&&n<s}}]),t}(Vt),gr=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments));return e.conversionPeriod=9,e.basePeriod=26,e.spanPeriod=52,e.displacement=26,e}return $(t,e),t}(ae),vr=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));r.result=[];var o=Object.assign({},{conversionPeriod:9,basePeriod:26,spanPeriod:52,displacement:26},e),l=new oe(2*o.conversionPeriod,!0,!0,!1),n=new oe(2*o.basePeriod,!0,!0,!1),a=new oe(2*o.spanPeriod,!0,!0,!1);return r.generator=regeneratorRuntime.mark(function e(){var t,r,s,u,p,i,h,g;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=void 0,r=void 0,s=X(o.conversionPeriod,o.basePeriod,o.spanPeriod,o.displacement),u=1,void(e.next=6);case 6:r=e.sent;case 7:return l.push(r.high),l.push(r.low),n.push(r.high),n.push(r.low),a.push(r.high),a.push(r.low),u<s?u++:(p=(l.periodHigh+l.periodLow)/2,i=(n.periodHigh+n.periodLow)/2,h=(p+i)/2,g=(a.periodHigh+a.periodLow)/2,t={conversion:p,base:i,spanA:h,spanB:g}),e.next=17,t;case 17:r=e.sent,e.next=7;break;case 20:case"end":return e.stop();}},e,this)})(),r.generator.next(),e.low.forEach(function(t,o){var l=r.generator.next({high:e.high[o],low:e.low[o]});l.value&&r.result.push(l.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){return this.generator.next(e).value}}]),t}(se);vr.calculate=j;var cr=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments));return e.maPeriod=20,e.atrPeriod=10,e.useSMA=!1,e.multiplier=1,e}return $(t,e),t}(ae),_r=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),dr=function(e){function t(e){Y(this,t);var r,o=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),l=e.useSMA?pe:ie,n=new l({period:e.maPeriod,values:[],format:function(e){return e}}),a=new Te({period:e.atrPeriod,high:[],low:[],close:[],format:function(e){return e}});o.result=[],o.generator=regeneratorRuntime.mark(function t(){var o,l,s,u,p;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return void(t.next=2);case 2:r=t.sent;case 3:return l=r,s=l.close,u=n.nextValue(s),p=a.nextValue(r),void 0!=u&&void 0!=p&&(o={middle:u,upper:u+e.multiplier*p,lower:u-e.multiplier*p}),t.next=10,o;case 10:r=t.sent,t.next=3;break;case 13:case"end":return t.stop();}},t,this)})(),o.generator.next();var s=e.high;return s.forEach(function(t,r){var l={high:t,low:e.low[r],close:e.close[r]},n=o.generator.next(l);void 0!=n.value&&o.result.push(n.value)}),o}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!=t.value)return t.value}}]),t}(se);dr.calculate=F;var xr=function(e){function t(){Y(this,t);var e=J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments));return e.period=22,e.multiplier=3,e}return $(t,e),t}(ae),mr=function(e){function t(){return Y(this,t),J(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return $(t,e),t}(ae),fr=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),o=e.high,l=e.low,n=e.close;r.result=[];var a=new Te({period:e.period,high:[],low:[],close:[],format:function(e){return e}}),s=new oe(2*e.period,!0,!0,!1);return r.generator=regeneratorRuntime.mark(function t(){var r,o,l,n,u,p;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return void(t.next=2);case 2:o=t.sent;case 3:return n=o,u=n.high,p=n.low,s.push(u),s.push(p),l=a.nextValue(o),s.totalPushed>=2*e.period&&void 0!=l&&(r={exitLong:s.periodHigh-l*e.multiplier,exitShort:s.periodLow+l*e.multiplier}),t.next=11,r;case 11:o=t.sent,t.next=3;break;case 14:case"end":return t.stop();}},t,this)})(),r.generator.next(),o.forEach(function(e,t){var o={high:e,low:l[t],close:n[t]},a=r.generator.next(o);void 0!=a.value&&r.result.push(a.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e){var t=this.generator.next(e);if(void 0!=t.value)return t.value}}]),t}(se);fr.calculate=W;var wr=function(e){function t(e,r){Y(this,t);var o=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return o.lineA=e,o.lineB=r,o}return $(t,e),t}(ae),yr=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));r.lineA=e.lineA,r.lineB=e.lineB;var o=[],l=[],n=regeneratorRuntime.mark(function e(){var t,r,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent,r=!1;case 4:for(o.unshift(t.valueA),l.unshift(t.valueB),r=t.valueA>t.valueB,n=1;!0===r&&o[n]>=l[n];)o[n]>l[n]?r=!1:o[n]<l[n]?r=!0:o[n]===l[n]&&(n+=1);return!0===r&&(o=[t.valueA],l=[t.valueB]),e.next=13,r;case 13:t=e.sent,e.next=4;break;case 16:case"end":return e.stop();}},e,this)});return r.generator=n(),r.generator.next(),r.result=[],r.lineA.forEach(function(e,t){var o=r.generator.next({valueA:r.lineA[t],valueB:r.lineB[t]});void 0!==o.value&&r.result.push(o.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e,t){return this.generator.next({valueA:e,valueB:t}).value}}],[{key:"reverseInputs",value:function(e){e.reversedInput&&(e.lineA?e.lineA.reverse():void 0,e.lineB?e.lineB.reverse():void 0)}}]),t}(se);yr.calculate=U;var Pr=function(e){function t(e,r){Y(this,t);var o=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return o.lineA=e,o.lineB=r,o}return $(t,e),t}(ae),kr=function(e){function t(e){Y(this,t);var r=J(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));r.lineA=e.lineA,r.lineB=e.lineB;var o=[],l=[],n=regeneratorRuntime.mark(function e(){var t,r,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return void(e.next=2);case 2:t=e.sent,r=!1;case 4:for(o.unshift(t.valueA),l.unshift(t.valueB),r=t.valueA<t.valueB,n=1;!0===r&&o[n]<=l[n];)o[n]<l[n]?r=!1:o[n]>l[n]?r=!0:o[n]===l[n]&&(n+=1);return!0===r&&(o=[t.valueA],l=[t.valueB]),e.next=13,r;case 13:t=e.sent,e.next=4;break;case 16:case"end":return e.stop();}},e,this)});return r.generator=n(),r.generator.next(),r.result=[],r.lineA.forEach(function(e,t){var o=r.generator.next({valueA:r.lineA[t],valueB:r.lineB[t]});void 0!==o.value&&r.result.push(o.value)}),r}return $(t,e),Z(t,[{key:"nextValue",value:function(e,t){return this.generator.next({valueA:e,valueB:t}).value}}],[{key:"reverseInputs",value:function(e){e.reversedInput&&(e.lineA?e.lineA.reverse():void 0,e.lineB?e.lineB.reverse():void 0)}}]),t}(se);kr.calculate=K;var Ir=G();e.getAvailableIndicators=G,e.AvailableIndicators=Ir,e.FixedSizeLinkedList=oe,e.CandleData=function e(){Y(this,e)},e.CandleList=le,e.sma=o,e.SMA=pe,e.ema=l,e.EMA=ie,e.wma=n,e.WMA=he,e.wema=a,e.WEMA=ge,e.macd=s,e.MACD=ce,e.rsi=i,e.RSI=we,e.bollingerbands=g,e.BollingerBands=Oe,e.adx=c,e.ADX=Le,e.atr=_,e.ATR=Te,e.truerange=v,e.TrueRange=Ae,e.roc=d,e.ROC=Me,e.kst=x,e.KST=je,e.psar=m,e.PSAR=We,e.stochastic=f,e.Stochastic=Ke,e.williamsr=w,e.WilliamsR=Ne,e.adl=y,e.ADL=Qe,e.obv=P,e.OBV=Ze,e.trix=k,e.TRIX=Je,e.forceindex=I,e.ForceIndex=tt,e.cci=O,e.CCI=ot,e.awesomeoscillator=V,e.AwesomeOscillator=nt,e.vwap=b,e.VWAP=st,e.volumeprofile=q,e.VolumeProfile=pt,e.mfi=E,e.MFI=vt,e.stochasticrsi=S,e.StochasticRSI=_t,e.averagegain=u,e.AverageGain=de,e.averageloss=p,e.AverageLoss=me,e.sd=h,e.SD=Pe,e.highest=A,e.Highest=xt,e.lowest=B,e.Lowest=ft,e.sum=H,e.Sum=yt,e.renko=L,e.HeikinAshi=Ot,e.heikinashi=z,e.bullish=function(e){return new Kt().hasPattern(e)},e.bearish=function(e){return new nr().hasPattern(e)},e.abandonedbaby=function(e){return new ar().hasPattern(e)},e.doji=function(e){return new St().hasPattern(e)},e.bearishengulfingpattern=function(e){return new Gt().hasPattern(e)},e.bullishengulfingpattern=function(e){return new Ct().hasPattern(e)},e.darkcloudcover=function(e){return new sr().hasPattern(e)},e.downsidetasukigap=function(e){return new Bt().hasPattern(e)},e.dragonflydoji=function(e){return new ur().hasPattern(e)},e.gravestonedoji=function(e){return new pr().hasPattern(e)},e.bullishharami=function(e){return new qt().hasPattern(e)},e.bearishharami=function(e){return new Nt().hasPattern(e)},e.bullishharamicross=function(e){return new Et().hasPattern(e)},e.bearishharamicross=function(e){return new Xt().hasPattern(e)},e.eveningdojistar=function(e){return new Qt().hasPattern(e)},e.eveningstar=function(e){return new Yt().hasPattern(e)},e.morningdojistar=function(e){return new At().hasPattern(e)},e.morningstar=function(e){return new bt().hasPattern(e)},e.bullishmarubozu=function(e){return new Ht().hasPattern(e)},e.bearishmarubozu=function(e){return new Zt().hasPattern(e)},e.piercingline=function(e){return new Lt().hasPattern(e)},e.bullishspinningtop=function(e){return new ir().hasPattern(e)},e.bearishspinningtop=function(e){return new hr().hasPattern(e)},e.threeblackcrows=function(e){return new $t().hasPattern(e)},e.threewhitesoldiers=function(e){return new zt().hasPattern(e)},e.bullishhammerstick=T,e.bearishhammerstick=M,e.bullishinvertedhammerstick=D,e.bearishinvertedhammerstick=R,e.hammerpattern=function(e){return new jt().hasPattern(e)},e.hammerpatternunconfirmed=function(e){return new Ft().hasPattern(e)},e.hangingman=function(e){return new Jt().hasPattern(e)},e.hangingmanunconfirmed=function(e){return new er().hasPattern(e)},e.shootingstar=function(e){return new tr().hasPattern(e)},e.shootingstarunconfirmed=function(e){return new rr().hasPattern(e)},e.tweezertop=function(e){return new or().hasPattern(e)},e.tweezerbottom=function(e){return new Wt().hasPattern(e)},e.fibonacciretracement=function(e,t){var r,o=[0,23.6,38.2,50,61.8,78.6,100,127.2,161.8,261.8,423.6];return r=e<t?o.map(function(r){var o=t-Q(e-t)*r/100;return 0<o?o:0}):o.map(function(r){var o=t+Q(e-t)*r/100;return 0<o?o:0}),r},e.ichimokucloud=j,e.IchimokuCloud=vr,e.keltnerchannels=F,e.KeltnerChannels=dr,e.KeltnerChannelsInput=cr,e.KeltnerChannelsOutput=_r,e.chandelierexit=W,e.ChandelierExit=fr,e.ChandelierExitInput=xr,e.ChandelierExitOutput=mr,e.crossUp=U,e.CrossUp=yr,e.crossDown=K,e.CrossDown=kr,e.setConfig=function(e,t){ne[e]=t},e.getConfig=t})(this.window=this.window||{});
