@echo off
chcp 65001 >nul
echo.
echo ===============================================
echo    إصلاح سريع لمشكلة analyzeMarket
echo ===============================================
echo.

echo 🔧 تطبيق الإصلاح السريع...
echo.

:: إنشاء نسخة احتياطية من preload.js
if exist "preload.js" (
    copy "preload.js" "preload.js.backup" >nul
    echo ✅ تم إنشاء نسخة احتياطية من preload.js
)

:: إنشاء preload.js محسن
echo 📝 إنشاء preload.js محسن...
node -e "
const fs = require('fs');
const preloadContent = \`const { contextBridge, ipcRenderer } = require('electron');

console.log('🔧 تحميل preload.js...');

// التحقق من توفر المكونات
if (!contextBridge) {
  console.error('❌ contextBridge غير متوفر');
  throw new Error('contextBridge غير متوفر');
}

if (!ipcRenderer) {
  console.error('❌ ipcRenderer غير متوفر');
  throw new Error('ipcRenderer غير متوفر');
}

console.log('✅ contextBridge و ipcRenderer متوفران');

try {
  console.log('🔧 تعريض API للواجهة الأمامية...');
  
  const api = {
    // تحليل السوق
    analyzeMarket: async () => {
      console.log('📡 استدعاء analyzeMarket من الواجهة...');
      try {
        const result = await ipcRenderer.invoke('analyze-market');
        console.log('✅ تم استلام النتيجة:', result ? 'نجح' : 'فشل');
        return result;
      } catch (error) {
        console.error('❌ خطأ في analyzeMarket:', error);
        throw error;
      }
    },
    
    // فحص الاتصال
    checkConnection: async () => {
      console.log('🌐 فحص الاتصال...');
      try {
        const result = await ipcRenderer.invoke('check-connection');
        console.log('✅ نتيجة فحص الاتصال:', result);
        return result;
      } catch (error) {
        console.error('❌ خطأ في فحص الاتصال:', error);
        return { connected: false, error: error.message };
      }
    },
    
    // معلومات النظام
    platform: process.platform,
    
    // إصدار التطبيق
    version: (() => {
      try {
        return require('./package.json').version;
      } catch (error) {
        console.warn('⚠️ لا يمكن قراءة إصدار التطبيق:', error.message);
        return '1.0.0';
      }
    })(),
    
    // دالة اختبار
    test: () => {
      console.log('🧪 اختبار API يعمل!');
      return 'API يعمل بشكل صحيح';
    }
  };
  
  console.log('🔧 تعريض API باستخدام contextBridge...');
  contextBridge.exposeInMainWorld('api', api);
  console.log('✅ تم تعريض API بنجاح');
  
} catch (error) {
  console.error('❌ خطأ في تعريض API:', error);
  console.error('📋 تفاصيل الخطأ:', error.stack);
}

// تسجيل الأحداث للتطوير
window.addEventListener('DOMContentLoaded', () => {
  console.log('✅ تم تحميل preload.js بنجاح');
  
  // اختبار توفر API
  setTimeout(() => {
    if (window.api) {
      console.log('✅ window.api متوفر:', Object.keys(window.api));
      
      // اختبار دالة test
      try {
        const testResult = window.api.test();
        console.log('🧪 نتيجة الاختبار:', testResult);
      } catch (error) {
        console.error('❌ خطأ في اختبار API:', error);
      }
    } else {
      console.error('❌ window.api غير متوفر!');
    }
  }, 100);
});
\`;

fs.writeFileSync('preload.js', preloadContent);
console.log('✅ تم إنشاء preload.js محسن');
"

:: تحديث renderer.js لإضافة تحققات أفضل
echo 📝 تحديث renderer.js...
node -e "
const fs = require('fs');
let content = fs.readFileSync('src/renderer.js', 'utf8');

// إضافة تحقق من window.api في بداية startAnalysis
const oldStartAnalysis = 'async function startAnalysis() {';
const newStartAnalysis = \`async function startAnalysis() {
    try {
        console.log('🔍 بدء التحليل...');
        
        // التحقق من توفر API
        if (!window.api) {
            throw new Error('API غير متوفر. يرجى إعادة تشغيل التطبيق.');
        }
        
        if (!window.api.analyzeMarket) {
            throw new Error('دالة analyzeMarket غير متوفرة. تحقق من إعدادات التطبيق.');
        }
        
        console.log('📡 API متوفر، بدء التحليل...');\`;

if (content.includes(oldStartAnalysis)) {
    content = content.replace(oldStartAnalysis, newStartAnalysis);
    fs.writeFileSync('src/renderer.js', content);
    console.log('✅ تم تحديث renderer.js');
} else {
    console.log('⚠️ لم يتم العثور على startAnalysis في renderer.js');
}
"

echo.
echo ✅ تم تطبيق الإصلاح السريع!
echo.
echo 🧪 اختبار الإصلاح...

:: اختبار تحميل preload.js
node -e "
try {
    // محاولة تحميل preload.js للتحقق من صحة الصيغة
    const vm = require('vm');
    const fs = require('fs');
    const preloadCode = fs.readFileSync('preload.js', 'utf8');
    
    // فحص أساسي للصيغة
    if (preloadCode.includes('contextBridge.exposeInMainWorld')) {
        console.log('✅ preload.js يحتوي على contextBridge.exposeInMainWorld');
    } else {
        console.log('❌ preload.js لا يحتوي على contextBridge.exposeInMainWorld');
    }
    
    if (preloadCode.includes('analyzeMarket')) {
        console.log('✅ preload.js يحتوي على analyzeMarket');
    } else {
        console.log('❌ preload.js لا يحتوي على analyzeMarket');
    }
    
    console.log('✅ preload.js يبدو صحيحاً');
    
} catch (error) {
    console.log('❌ خطأ في preload.js:', error.message);
}
"

echo.
echo 🚀 الخطوات التالية:
echo.
echo 1. شغل التطبيق: npm start
echo 2. افتح أدوات المطور (F12)
echo 3. راقب رسائل Console
echo 4. جرب زر "حلل الآن"
echo.
echo إذا استمرت المشكلة:
echo - شغل: npm run debug
echo - أو شغل: npm run test-ui
echo.

pause
