{"name": "bitcoin-analyzer-electron", "version": "1.0.0", "description": "تطبيق تحليل البتكوين الاحترافي", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "analyze": "node run-analysis.js", "demo": "node demo.js", "test": "node test/test.js", "test-analysis": "node test-analysis.js", "help": "node run-analysis.js --help"}, "keywords": ["bitcoin", "crypto", "trading", "analysis", "electron"], "author": "Bitcoin Analyzer", "license": "MIT", "dependencies": {"axios": "^1.10.0", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^2.30.0", "regression": "^2.0.1", "technicalindicators": "^3.1.0"}, "devDependencies": {"electron": "^27.3.11"}, "build": {"appId": "com.bitcoinanalyzer.app", "productName": "Bitcoin Analyzer Pro", "directories": {"output": "dist"}, "files": ["**/*", "!test/**/*", "!*.md"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}