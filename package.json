{"name": "bitcoin-analyzer-electron", "version": "1.0.0", "description": "تطبيق تحليل البتكوين الاحترافي", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "analyze": "node run-analysis.js", "demo": "node demo.js", "test": "node test/test.js", "test-analysis": "node test-analysis.js", "help": "node run-analysis.js --help", "build": "electron-builder", "build:win": "electron-builder --win", "build:portable": "electron-builder --win portable", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir"}, "keywords": ["bitcoin", "crypto", "trading", "analysis", "electron"], "author": "Bitcoin Analyzer", "license": "MIT", "dependencies": {"axios": "^1.10.0", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^2.30.0", "regression": "^2.0.1", "technicalindicators": "^3.1.0"}, "devDependencies": {"electron": "^27.3.11", "electron-builder": "^24.13.3"}, "build": {"appId": "com.bitcoinanalyzer.app", "productName": "Bitcoin Analyzer Pro", "copyright": "Copyright © 2024 Bitcoin Analyzer Pro", "directories": {"output": "dist", "buildResources": "build"}, "files": ["**/*", "!test/**/*", "!test-analysis.js", "!demo.js", "!run-analysis.js", "!*.md", "!.giti<PERSON>re", "!.git/**/*"], "extraResources": [{"from": "README.md", "to": "README.md"}, {"from": "QUICKSTART.md", "to": "QUICKSTART.md"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Bitcoin Analyzer Pro"}, "portable": {"artifactName": "${productName}-${version}-Portable.${ext}"}, "mac": {"target": "dmg", "icon": "build/icon.icns", "category": "public.app-category.finance"}, "linux": {"target": "AppImage", "icon": "build/icon.png", "category": "Office"}}}