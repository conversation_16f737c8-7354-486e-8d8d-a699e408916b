<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bitcoinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f7931a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffb347;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" stroke="#ffffff" stroke-width="8"/>
  
  <!-- Bitcoin symbol -->
  <g transform="translate(256,256)">
    <!-- Bitcoin B -->
    <path d="M-60,-80 L-60,80 L-40,80 L-40,60 L20,60 C40,60 60,40 60,20 C60,5 50,-5 35,-10 C50,-15 55,-25 55,-40 C55,-60 35,-80 15,-80 L-40,-80 L-40,-100 L-60,-100 Z M-40,-60 L15,-60 C25,-60 35,-50 35,-40 C35,-30 25,-20 15,-20 L-40,-20 Z M-40,0 L20,0 C30,0 40,10 40,20 C40,30 30,40 20,40 L-40,40 Z" 
          fill="url(#bitcoinGradient)" 
          stroke="#ffffff" 
          stroke-width="3"/>
    
    <!-- Vertical lines for Bitcoin symbol -->
    <rect x="-50" y="-100" width="8" height="20" fill="url(#bitcoinGradient)"/>
    <rect x="-50" y="80" width="8" height="20" fill="url(#bitcoinGradient)"/>
    <rect x="-30" y="-100" width="8" height="20" fill="url(#bitcoinGradient)"/>
    <rect x="-30" y="80" width="8" height="20" fill="url(#bitcoinGradient)"/>
  </g>
  
  <!-- Chart elements -->
  <g transform="translate(100,350)">
    <!-- Chart bars -->
    <rect x="0" y="0" width="15" height="40" fill="#27ae60" opacity="0.8"/>
    <rect x="20" y="-10" width="15" height="50" fill="#27ae60" opacity="0.8"/>
    <rect x="40" y="10" width="15" height="30" fill="#e74c3c" opacity="0.8"/>
    <rect x="60" y="-5" width="15" height="45" fill="#27ae60" opacity="0.8"/>
    <rect x="80" y="5" width="15" height="35" fill="#27ae60" opacity="0.8"/>
    
    <!-- Trend line -->
    <polyline points="7,20 27,10 47,25 67,15 87,20" 
              stroke="#3498db" 
              stroke-width="3" 
              fill="none" 
              opacity="0.9"/>
  </g>
  
  <!-- Analysis symbols -->
  <g transform="translate(350,150)">
    <!-- Magnifying glass -->
    <circle cx="0" cy="0" r="25" fill="none" stroke="#ffffff" stroke-width="4"/>
    <line x1="18" y1="18" x2="35" y2="35" stroke="#ffffff" stroke-width="4" stroke-linecap="round"/>
  </g>
  
  <!-- Decorative elements -->
  <g opacity="0.3">
    <circle cx="100" cy="100" r="3" fill="#ffffff"/>
    <circle cx="400" cy="120" r="2" fill="#ffffff"/>
    <circle cx="450" cy="400" r="4" fill="#ffffff"/>
    <circle cx="80" cy="450" r="2" fill="#ffffff"/>
  </g>
</svg>
