const { contextBridge, ipc<PERSON>enderer } = require('electron');

console.log('🔧 تحميل preload.js...');

// التحقق من توفر المكونات
if (!contextBridge) {
  console.error('❌ contextBridge غير متوفر');
  throw new Error('contextBridge غير متوفر');
}

if (!ipcRenderer) {
  console.error('❌ ipcRenderer غير متوفر');
  throw new Error('ipcRenderer غير متوفر');
}

console.log('✅ contextBridge و ipcRenderer متوفران');

// تعريض API آمن للواجهة الأمامية
try {
  console.log('🔧 تعريض API للواجهة الأمامية...');

  const api = {
    // تحليل السوق
    analyzeMarket: async () => {
      console.log('📡 استدعاء analyzeMarket من الواجهة...');
      try {
        const result = await ipcRenderer.invoke('analyze-market');
        console.log('✅ تم استلام النتيجة:', result ? 'نجح' : 'فشل');
        return result;
      } catch (error) {
        console.error('❌ خطأ في analyzeMarket:', error);
        throw error;
      }
    },

    // فحص الاتصال
    checkConnection: async () => {
      console.log('🌐 فحص الاتصال...');
      try {
        const result = await ipcRenderer.invoke('check-connection');
        console.log('✅ نتيجة فحص الاتصال:', result);
        return result;
      } catch (error) {
        console.error('❌ خطأ في فحص الاتصال:', error);
        return { connected: false, error: error.message };
      }
    },

    // معلومات النظام
    platform: process.platform,

    // إصدار التطبيق
    version: (() => {
      try {
        return require('./package.json').version;
      } catch (error) {
        console.warn('⚠️ لا يمكن قراءة إصدار التطبيق:', error.message);
        return '1.0.0';
      }
    })(),

    // دالة اختبار
    test: () => {
      console.log('🧪 اختبار API يعمل!');
      return 'API يعمل بشكل صحيح';
    }
  };

  console.log('🔧 تعريض API باستخدام contextBridge...');
  contextBridge.exposeInMainWorld('api', api);
  console.log('✅ تم تعريض API بنجاح');

} catch (error) {
  console.error('❌ خطأ في تعريض API:', error);
  console.error('📋 تفاصيل الخطأ:', error.stack);
}

// تسجيل الأحداث للتطوير
window.addEventListener('DOMContentLoaded', () => {
  console.log('✅ تم تحميل preload.js بنجاح');

  // اختبار توفر API
  setTimeout(() => {
    if (window.api) {
      console.log('✅ window.api متوفر:', Object.keys(window.api));

      // اختبار دالة test
      try {
        const testResult = window.api.test();
        console.log('🧪 نتيجة الاختبار:', testResult);
      } catch (error) {
        console.error('❌ خطأ في اختبار API:', error);
      }
    } else {
      console.error('❌ window.api غير متوفر!');
    }
  }, 100);
});
