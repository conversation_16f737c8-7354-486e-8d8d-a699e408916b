@echo off
chcp 65001 >nul
echo.
echo ===============================================
echo    إنشاء حزمة التوزيع النهائية
echo ===============================================
echo.

set "package_name=Bitcoin-Analyzer-Pro-Final-v1.0.0"

echo 📦 إنشاء حزمة التوزيع النهائية...
echo.

:: إنشاء مجلد التوزيع
if exist "%package_name%" rmdir /s /q "%package_name%"
mkdir "%package_name%"

echo ✅ تم إنشاء مجلد التوزيع: %package_name%

:: نسخ الملفات التنفيذية
echo.
echo 📁 نسخ الملفات التنفيذية...

if exist "dist\Bitcoin Analyzer Pro-1.0.0-x64.exe" (
    copy "dist\Bitcoin Analyzer Pro-1.0.0-x64.exe" "%package_name%\"
    echo ✅ تم نسخ ملف Installer
) else (
    echo ❌ ملف Installer غير موجود
)

if exist "dist\Bitcoin Analyzer Pro-1.0.0-Portable.exe" (
    copy "dist\Bitcoin Analyzer Pro-1.0.0-Portable.exe" "%package_name%\"
    echo ✅ تم نسخ ملف Portable
) else (
    echo ❌ ملف Portable غير موجود
)

:: إنشاء ملف تعليمات شامل
echo.
echo 📝 إنشاء ملف التعليمات الشامل...

node -e "
const fs = require('fs');
const instructionsContent = \`===============================================
    محلل البتكوين الاحترافي - الإصدار النهائي 1.0.0
===============================================

🎉 مرحباً بك في محلل البتكوين الاحترافي!

📋 محتويات هذه الحزمة:
========================

📦 Bitcoin Analyzer Pro-1.0.0-x64.exe (ملف التثبيت)
   - حجم الملف: ~80 MB
   - يقوم بتثبيت التطبيق في النظام
   - ينشئ اختصارات في سطح المكتب وقائمة ابدأ
   - يتضمن أداة إلغاء تثبيت
   - مستحسن للاستخدام العادي

💼 Bitcoin Analyzer Pro-1.0.0-Portable.exe (النسخة المحمولة)
   - حجم الملف: ~72 MB
   - لا تحتاج تثبيت
   - تعمل من أي مكان
   - مناسبة للـ USB أو التخزين السحابي
   - تشغيل فوري

📄 تعليمات التشغيل التفصيلية.txt
   - تعليمات مفصلة باللغة العربية
   - حلول للمشاكل الشائعة
   - نصائح للاستخدام الأمثل

📄 اقرأني أولاً.txt
   - مقدمة سريعة للتطبيق
   - خطوات البدء السريع

🚀 البدء السريع:
================

للمبتدئين (مستحسن):
--------------------
1. انقر نقراً مزدوجاً على 'Bitcoin Analyzer Pro-1.0.0-x64.exe'
2. اتبع تعليمات التثبيت
3. شغل التطبيق من سطح المكتب أو قائمة ابدأ
4. انقر على 'ابدأ التحليل الآن' أو 'حلل الآن'

للمستخدمين المتقدمين:
----------------------
1. انقر نقراً مزدوجاً على 'Bitcoin Analyzer Pro-1.0.0-Portable.exe'
2. التطبيق سيعمل مباشرة بدون تثبيت
3. انقر على 'حلل الآن' لبدء التحليل

⚙️ متطلبات النظام:
==================
✅ Windows 10 أو Windows 11 (64-bit)
✅ 4GB ذاكرة عشوائية (مستحسن)
✅ 500MB مساحة فارغة على القرص الصلب
✅ اتصال بالإنترنت (لجلب بيانات البتكوين)
✅ معالج Intel/AMD x64

🎯 ما يقوم به التطبيق:
======================
📊 تحليل فني شامل للبتكوين باستخدام 12+ مؤشر فني متقدم:
   ✅ RSI (مؤشر القوة النسبية)
   ✅ MACD (تقارب وتباعد المتوسطات)
   ✅ EMA (المتوسطات المتحركة الأسية)
   ✅ SMA (المتوسطات المتحركة البسيطة)
   ✅ Bollinger Bands (نطاقات بولينجر)
   ✅ ATR (متوسط المدى الحقيقي)
   ✅ ADX (مؤشر الاتجاه المتوسط)
   ✅ Stochastic (مذبذب ستوكاستيك)
   ✅ OBV (حجم التوازن)
   ✅ Ichimoku (سحابة إيشيموكو)
   ✅ Fibonacci (مستويات فيبوناتشي)

🔮 توقعات سعرية للـ 24-48 ساعة القادمة
🎯 توصيات استثمارية ذكية (شراء/بيع/انتظار)
📋 تحليل الأنماط والاتجاهات
📊 رسوم بيانية تفاعلية
📈 تحليل المخاطر ومستوى الثقة

🔧 استكشاف الأخطاء:
====================

المشكلة: التطبيق لا يفتح
الحل:
- تأكد من أن نظامك Windows 10/11 (64-bit)
- شغل الملف كمدير (انقر بالزر الأيمن > تشغيل كمدير)
- تأكد من أن Windows Defender لا يحجب التطبيق
- أعد تشغيل الكمبيوتر وحاول مرة أخرى

المشكلة: خطأ 'Cannot read properties of undefined'
الحل:
- أغلق التطبيق وأعد فتحه
- تأكد من اتصال الإنترنت
- جرب النسخة الأخرى (Portable بدلاً من Installer أو العكس)

المشكلة: خطأ في جلب البيانات
الحل:
- تحقق من اتصال الإنترنت
- تأكد من أن Firewall لا يحجب التطبيق
- جرب استخدام VPN إذا كانت APIs محجوبة في بلدك
- انتظر قليلاً وأعد المحاولة

المشكلة: التطبيق بطيء
الحل:
- أغلق التطبيقات الأخرى لتوفير ذاكرة
- تأكد من وجود مساحة كافية على القرص الصلب
- أعد تشغيل التطبيق

⚠️ تنويه مهم:
==============
هذا التطبيق للأغراض التعليمية والبحثية فقط.
لا يُعتبر نصيحة استثمارية أو مالية.
استشر خبير مالي مؤهل قبل اتخاذ قرارات استثمارية.
الاستثمار في العملات المشفرة ينطوي على مخاطر عالية.

🔒 الخصوصية والأمان:
====================
✅ التطبيق لا يجمع أي بيانات شخصية
✅ جميع العمليات تتم محلياً على جهازك
✅ يستخدم فقط APIs عامة ومجانية
✅ مفتوح المصدر - يمكن مراجعة الكود
✅ لا يحتوي على malware أو برامج ضارة
✅ تم اختباره على Windows Defender

📞 الدعم:
==========
إذا واجهت أي مشاكل:
1. اقرأ 'تعليمات التشغيل التفصيلية.txt'
2. تأكد من توفر متطلبات النظام
3. جرب الحلول المقترحة أعلاه
4. أعد تشغيل الكمبيوتر

🎉 استمتع بالتحليل الاحترافي للبتكوين!

===============================================
© 2024 Bitcoin Analyzer Pro - جميع الحقوق محفوظة
تم التطوير باستخدام Electron و Node.js
الإصدار: 1.0.0 النهائي
تاريخ البناء: \${new Date().toLocaleDateString('ar-SA')}
===============================================\`;

fs.writeFileSync('%package_name%/اقرأني أولاً.txt', instructionsContent);
console.log('✅ تم إنشاء ملف التعليمات الأساسي');
"

:: إنشاء ملف تعليمات تفصيلي
echo.
echo 📝 إنشاء ملف التعليمات التفصيلية...

node -e "
const fs = require('fs');
const detailedInstructions = \`===============================================
    تعليمات التشغيل التفصيلية
    محلل البتكوين الاحترافي v1.0.0
===============================================

🔧 تعليمات التثبيت والتشغيل:
=============================

📦 طريقة التثبيت العادي (مستحسن):
----------------------------------
1. انقر نقراً مزدوجاً على 'Bitcoin Analyzer Pro-1.0.0-x64.exe'
2. إذا ظهرت رسالة Windows Defender، انقر 'More info' ثم 'Run anyway'
3. اختر لغة التثبيت (العربية أو الإنجليزية)
4. اقرأ واقبل اتفاقية الترخيص
5. اختر مجلد التثبيت (افتراضي: C:\\Program Files\\Bitcoin Analyzer Pro)
6. اختر إنشاء اختصارات:
   ✅ اختصار سطح المكتب
   ✅ اختصار قائمة ابدأ
7. انقر 'Install' وانتظر اكتمال التثبيت
8. انقر 'Finish' لإكمال التثبيت

💼 طريقة التشغيل المحمول:
--------------------------
1. انقر نقراً مزدوجاً على 'Bitcoin Analyzer Pro-1.0.0-Portable.exe'
2. إذا ظهرت رسالة Windows Defender، انقر 'More info' ثم 'Run anyway'
3. التطبيق سيعمل مباشرة بدون تثبيت
4. يمكن نسخ الملف إلى USB أو أي مكان آخر

🚀 كيفية الاستخدام:
===================

1. 📊 عند فتح التطبيق:
   - ستظهر شاشة ترحيب باللغة العربية
   - انقر 'ابدأ التحليل الآن' أو 'حلل الآن'

2. 🔍 أثناء التحليل:
   - سيتم عرض 'جاري التحليل...' مع شريط تقدم
   - سيتم جلب البيانات من الإنترنت (CoinGecko/Binance)
   - سيتم حساب جميع المؤشرات الفنية
   - سيتم تحليل الأنماط والاتجاهات
   - سيتم إعداد التوقع السعري والتوصية

3. 📈 النتائج:
   - السعر الحالي للبتكوين
   - التوصية النهائية (شراء/بيع/انتظار)
   - مستوى الثقة في التحليل (1-10)
   - التوقع السعري للـ 24-48 ساعة
   - جميع المؤشرات الفنية بالتفصيل
   - الرسم البياني التفاعلي
   - تحليل المخاطر

4. 🔄 التحديث:
   - انقر 'حلل الآن' للحصول على بيانات محدثة
   - يُنصح بإعادة التحليل كل ساعة للحصول على أحدث البيانات

📊 شرح المؤشرات الفنية:
========================

🔴 RSI (مؤشر القوة النسبية):
- القيم فوق 70: منطقة تشبع شرائي (إشارة بيع محتملة)
- القيم تحت 30: منطقة تشبع بيعي (إشارة شراء محتملة)
- القيم بين 30-70: منطقة متوازنة

🔵 MACD (تقارب وتباعد المتوسطات):
- عندما يعبر MACD فوق خط الإشارة: إشارة شراء
- عندما يعبر MACD تحت خط الإشارة: إشارة بيع
- التباعد يشير لقوة الاتجاه

🟢 EMA (المتوسط المتحرك الأسي):
- السعر فوق EMA: اتجاه صاعد
- السعر تحت EMA: اتجاه هابط
- تقاطع EMAs المختلفة يعطي إشارات

🟡 Bollinger Bands (نطاقات بولينجر):
- السعر عند النطاق العلوي: تشبع شرائي محتمل
- السعر عند النطاق السفلي: تشبع بيعي محتمل
- ضيق النطاقات: توقع حركة قوية قادمة

🟣 Stochastic (مذبذب ستوكاستيك):
- القيم فوق 80: تشبع شرائي
- القيم تحت 20: تشبع بيعي
- تقاطع الخطوط يعطي إشارات

🔶 ADX (مؤشر الاتجاه المتوسط):
- القيم فوق 25: اتجاه قوي
- القيم تحت 20: اتجاه ضعيف أو عدم وجود اتجاه
- لا يحدد اتجاه الحركة، فقط قوتها

🎯 فهم التوصيات:
=================

📈 توصية 'شراء':
- المؤشرات تدعم الاتجاه الصاعد
- توقع ارتفاع السعر في الفترة القادمة
- مستوى ثقة عالي في التحليل

📉 توصية 'بيع':
- المؤشرات تدعم الاتجاه الهابط
- توقع انخفاض السعر في الفترة القادمة
- مستوى ثقة عالي في التحليل

⏸️ توصية 'انتظار':
- المؤشرات متضاربة أو غير واضحة
- السوق في حالة تذبذب أو عدم تأكد
- يُنصح بانتظار إشارات أوضح

🔧 حل المشاكل الشائعة:
=======================

❌ مشكلة: 'Cannot read properties of undefined (reading analyzeMarket)'
✅ الحل:
1. أغلق التطبيق تماماً
2. أعد فتح التطبيق
3. انتظر 2-3 ثوان قبل النقر على أي زر
4. إذا استمرت المشكلة، أعد تشغيل الكمبيوتر

❌ مشكلة: 'خطأ في جلب البيانات' أو 'فشل الاتصال'
✅ الحل:
1. تحقق من اتصال الإنترنت
2. جرب إغلاق وإعادة فتح التطبيق
3. تأكد من أن Firewall لا يحجب التطبيق
4. إذا كنت تستخدم VPN، جرب إيقافه أو تغييره
5. انتظر دقيقة وأعد المحاولة

❌ مشكلة: التطبيق لا يفتح أو يتوقف فجأة
✅ الحل:
1. تأكد من أن نظامك Windows 10 أو 11 (64-bit)
2. شغل التطبيق كمدير (انقر بالزر الأيمن > Run as administrator)
3. تأكد من وجود مساحة كافية على القرص الصلب (500MB على الأقل)
4. أغلق التطبيقات الأخرى لتوفير ذاكرة
5. أعد تشغيل الكمبيوتر

❌ مشكلة: Windows Defender يحجب التطبيق
✅ الحل:
1. عند ظهور رسالة التحذير، انقر 'More info'
2. انقر 'Run anyway'
3. أو أضف التطبيق لقائمة الاستثناءات في Windows Defender

❌ مشكلة: التطبيق بطيء أو يستهلك ذاكرة كثيرة
✅ الحل:
1. أغلق التطبيقات الأخرى غير الضرورية
2. تأكد من وجود 4GB ذاكرة عشوائية على الأقل
3. أعد تشغيل التطبيق كل فترة
4. لا تشغل عدة نسخ من التطبيق في نفس الوقت

🔒 معلومات الأمان:
==================

✅ التطبيق آمن تماماً:
- لا يجمع أي بيانات شخصية
- لا يصل لملفاتك الشخصية
- يستخدم فقط APIs عامة للحصول على أسعار البتكوين
- جميع العمليات تتم محلياً على جهازك
- لا يرسل أي معلومات لخوادم خارجية

✅ مصادر البيانات:
- CoinGecko API (مجاني وعام)
- Binance API (مجاني وعام)
- لا يتطلب تسجيل أو حساب

📱 نصائح للاستخدام الأمثل:
============================

🕐 التوقيت:
- أفضل أوقات التحليل: عند فتح الأسواق الأمريكية والآسيوية
- تجنب التحليل أثناء الأخبار المهمة أو الأحداث الكبرى
- حلل مرة كل ساعة للحصول على أحدث البيانات

📊 تفسير النتائج:
- لا تعتمد على توصية واحدة فقط
- انظر لمستوى الثقة في التحليل
- راجع عدة مؤشرات قبل اتخاذ القرار
- استخدم التحليل كأداة مساعدة وليس قرار نهائي

💡 نصائح عامة:
- احتفظ بنسخة احتياطية من ملف التطبيق
- لا تستثمر أكثر مما تستطيع خسارته
- تعلم أساسيات التحليل الفني
- استشر خبير مالي للقرارات الكبيرة

===============================================
انتهت التعليمات التفصيلية
للدعم: راجع الملفات الأخرى في الحزمة
===============================================\`;

fs.writeFileSync('%package_name%/تعليمات التشغيل التفصيلية.txt', detailedInstructions);
console.log('✅ تم إنشاء ملف التعليمات التفصيلية');
"

:: نسخ ملفات إضافية
echo.
echo 📁 نسخ ملفات إضافية...

if exist "README.md" (
    copy "README.md" "%package_name%\"
    echo ✅ تم نسخ README.md
)

if exist "QUICKSTART.md" (
    copy "QUICKSTART.md" "%package_name%\"
    echo ✅ تم نسخ QUICKSTART.md
)

:: إنشاء ملف معلومات النظام
echo.
echo 📝 إنشاء ملف معلومات النظام...

node -e "
const fs = require('fs');
const systemInfo = \`===============================================
    معلومات النظام والتقنيات المستخدمة
===============================================

🖥️ متطلبات النظام:
===================
- نظام التشغيل: Windows 10/11 (64-bit)
- المعالج: Intel/AMD x64
- الذاكرة: 4GB RAM (مستحسن)
- المساحة: 500MB مساحة فارغة
- الإنترنت: مطلوب لجلب البيانات

🛠️ التقنيات المستخدمة:
=======================
- Electron: إطار عمل التطبيق
- Node.js: بيئة التشغيل
- JavaScript: لغة البرمجة
- HTML/CSS: الواجهة
- Chart.js: الرسوم البيانية
- Technical Indicators: المؤشرات الفنية
- Axios: جلب البيانات
- Regression: التنبؤ السعري

📊 مصادر البيانات:
==================
- CoinGecko API: أسعار وبيانات السوق
- Binance API: بيانات تاريخية
- جميع المصادر مجانية وعامة

🔧 معلومات البناء:
==================
- الإصدار: 1.0.0
- تاريخ البناء: \${new Date().toLocaleDateString('ar-SA')}
- حجم Installer: ~80MB
- حجم Portable: ~72MB
- حجم التطبيق المفكوك: ~200MB

📋 الملفات المضمنة:
====================
- جميع مكتبات Node.js
- Electron Runtime
- مكتبات النظام المطلوبة
- الواجهة العربية
- الأيقونات والموارد

===============================================\`;

fs.writeFileSync('%package_name%/معلومات النظام.txt', systemInfo);
console.log('✅ تم إنشاء ملف معلومات النظام');
"

:: عرض معلومات الحزمة
echo.
echo 📋 معلومات الحزمة النهائية:
echo.
dir /b "%package_name%"

echo.
echo ✅ تم إنشاء حزمة التوزيع النهائية بنجاح!
echo.
echo 📦 اسم الحزمة: %package_name%
echo 📁 المحتويات:
echo    - ملف Installer (.exe)
echo    - ملف Portable (.exe)  
echo    - تعليمات شاملة (عربي)
echo    - معلومات النظام
echo    - ملفات إضافية
echo.
echo 🚀 الحزمة جاهزة للتوزيع!
echo.

:: فتح مجلد الحزمة
start "" "%package_name%"

pause
