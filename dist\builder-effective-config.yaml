directories:
  output: dist
  buildResources: build
appId: com.bitcoinanalyzer.app
productName: Bitcoin Analyzer Pro
copyright: Copyright © 2024 Bitcoin Analyzer Pro
files:
  - filter:
      - '**/*'
      - '!test/**/*'
      - '!test-analysis.js'
      - '!demo.js'
      - '!run-analysis.js'
      - '!*.md'
      - '!.gitignore'
      - '!.git/**/*'
extraResources:
  - from: README.md
    to: README.md
  - from: QUICKSTART.md
    to: QUICKSTART.md
win:
  target: nsis
  icon: build/icon.ico
  requestedExecutionLevel: asInvoker
  artifactName: ${productName}-${version}-${arch}.${ext}
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Bitcoin Analyzer Pro
portable:
  artifactName: ${productName}-${version}-Portable.${ext}
mac:
  target: dmg
  icon: build/icon.icns
  category: public.app-category.finance
linux:
  target: AppImage
  icon: build/icon.png
  category: Office
electronVersion: 27.3.11
