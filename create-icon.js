// إنشاء أيقونة بسيطة للتطبيق
const fs = require('fs');
const path = require('path');

// إنشاء أيقونة ICO بسيطة (256x256 pixels)
const size = 256;
const iconData = Buffer.from([
    // ICO header
    0x00, 0x00, // Reserved
    0x01, 0x00, // Type (1 = ICO)
    0x01, 0x00, // Number of images

    // Image directory entry
    0x00, // Width (0 = 256)
    0x00, // Height (0 = 256)
    0x00, // Color count (0 = no palette)
    0x00, // Reserved
    0x01, 0x00, // Color planes
    0x20, 0x00, // Bits per pixel (32)
    0x00, 0x00, 0x04, 0x00, // Size of image data (262144 bytes)
    0x16, 0x00, 0x00, 0x00, // Offset to image data

    // Bitmap info header
    0x28, 0x00, 0x00, 0x00, // Header size (40)
    0x00, 0x01, 0x00, 0x00, // Width (256)
    0x00, 0x02, 0x00, 0x00, // Height (512 = 256*2 for ICO)
    0x01, 0x00, // Planes
    0x20, 0x00, // Bits per pixel (32)
    0x00, 0x00, 0x00, 0x00, // Compression
    0x00, 0x00, 0x04, 0x00, // Image size
    0x00, 0x00, 0x00, 0x00, // X pixels per meter
    0x00, 0x00, 0x00, 0x00, // Y pixels per meter
    0x00, 0x00, 0x00, 0x00, // Colors used
    0x00, 0x00, 0x00, 0x00, // Important colors
]);

// إضافة بيانات الصورة (256x256 pixels, 32-bit RGBA)
const imageData = Buffer.alloc(size * size * 4);

// ملء الصورة بلون أزرق مع دائرة ذهبية
for (let y = 0; y < size; y++) {
    for (let x = 0; x < size; x++) {
        const index = (y * size + x) * 4;

        // حساب المسافة من المركز
        const centerX = size / 2;
        const centerY = size / 2;
        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);

        if (distance <= size * 0.35) {
            // دائرة ذهبية
            imageData[index] = 0x1A; // Blue
            imageData[index + 1] = 0x93; // Green
            imageData[index + 2] = 0xF7; // Red (BGR format)
            imageData[index + 3] = 0xFF; // Alpha
        } else {
            // خلفية زرقاء
            imageData[index] = 0xEA; // Blue
            imageData[index + 1] = 0x7E; // Green
            imageData[index + 2] = 0x66; // Red (BGR format)
            imageData[index + 3] = 0xFF; // Alpha
        }
    }
}

// AND mask (256x256 bits = 8192 bytes)
const andMask = Buffer.alloc(size * size / 8, 0x00);

// دمج جميع البيانات
const fullIconData = Buffer.concat([iconData, imageData, andMask]);

// كتابة الملف
const buildDir = path.join(__dirname, 'build');
if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir, { recursive: true });
}

fs.writeFileSync(path.join(buildDir, 'icon.ico'), fullIconData);

console.log('✅ تم إنشاء أيقونة بسيطة: build/icon.ico');
console.log('📁 حجم الملف:', fullIconData.length, 'bytes');

// إنشاء أيقونة PNG أيضاً
const pngHeader = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A // PNG signature
]);

// هذا مجرد ملف PNG بسيط جداً للاختبار
const simplePng = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x10, // Width: 16
    0x00, 0x00, 0x00, 0x10, // Height: 16
    0x08, 0x02, 0x00, 0x00, 0x00, // Bit depth: 8, Color type: 2 (RGB), Compression: 0, Filter: 0, Interlace: 0
    0x90, 0x91, 0x68, 0x36, // CRC
    0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, // Compressed data
    0x00, 0x01, 0x00, 0x25, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
]);

fs.writeFileSync(path.join(buildDir, 'icon.png'), simplePng);
console.log('✅ تم إنشاء أيقونة PNG: build/icon.png');
