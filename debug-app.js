// ملف اختبار لتشخيص مشاكل التطبيق
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

console.log('🔧 بدء تشخيص التطبيق...');

let mainWindow;

function createWindow() {
  console.log('🪟 إنشاء النافذة الرئيسية...');
  
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true // تفعيل أدوات المطور
    },
    icon: path.join(__dirname, 'assets', 'icon.png'),
    title: 'Bitcoin Analyzer Pro - Debug Mode',
    show: false
  });

  console.log('📁 مسار preload:', path.join(__dirname, 'preload.js'));
  console.log('📁 مسار HTML:', path.join(__dirname, 'src', 'index.html'));

  mainWindow.loadFile('src/index.html');

  // إظهار النافذة عند اكتمال التحميل
  mainWindow.once('ready-to-show', () => {
    console.log('✅ النافذة جاهزة للعرض');
    mainWindow.show();
    
    // فتح أدوات المطور تلقائياً
    mainWindow.webContents.openDevTools();
  });

  // تسجيل أحداث التحميل
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('✅ تم تحميل الصفحة بنجاح');
  });

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('❌ فشل تحميل الصفحة:', errorCode, errorDescription);
  });

  // تسجيل رسائل console من الواجهة
  mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    console.log(`[RENDERER ${level}] ${message}`);
  });
}

// معالج تحليل السوق مع تسجيل مفصل
ipcMain.handle('analyze-market', async () => {
  console.log('📡 تم استلام طلب analyze-market');
  
  try {
    console.log('🔍 بدء تحليل السوق...');
    
    // محاولة تحميل مكتبة التحليل
    let analyzeMarket;
    try {
      const signals = require('./lib/signals');
      analyzeMarket = signals.analyzeMarket;
      console.log('✅ تم تحميل مكتبة signals بنجاح');
    } catch (error) {
      console.error('❌ خطأ في تحميل مكتبة signals:', error.message);
      return {
        success: false,
        error: `خطأ في تحميل مكتبة التحليل: ${error.message}`
      };
    }

    // تنفيذ التحليل
    const analysis = await analyzeMarket();
    console.log('✅ تم إكمال التحليل بنجاح');
    console.log('📊 نوع النتيجة:', typeof analysis);
    console.log('📊 مفاتيح النتيجة:', analysis ? Object.keys(analysis) : 'لا توجد بيانات');
    
    return {
      success: true,
      data: analysis
    };
  } catch (error) {
    console.error('❌ خطأ في تحليل السوق:', error);
    console.error('📋 تفاصيل الخطأ:', error.stack);
    
    return {
      success: false,
      error: error.message || 'خطأ غير معروف في التحليل'
    };
  }
});

// معالج فحص الاتصال مع تسجيل مفصل
ipcMain.handle('check-connection', async () => {
  console.log('🌐 فحص حالة الاتصال...');
  
  try {
    const axios = require('axios');
    console.log('📡 محاولة الاتصال بـ CoinGecko...');
    
    const response = await axios.get('https://api.coingecko.com/api/v3/ping', { 
      timeout: 5000 
    });
    
    console.log('✅ الاتصال ناجح:', response.status);
    return { connected: true };
    
  } catch (error) {
    console.error('❌ فشل الاتصال:', error.message);
    return { 
      connected: false, 
      error: error.message 
    };
  }
});

// معالج اختبار بسيط
ipcMain.handle('test-api', async () => {
  console.log('🧪 اختبار API...');
  return {
    success: true,
    message: 'API يعمل بشكل صحيح',
    timestamp: new Date().toISOString()
  };
});

app.whenReady().then(() => {
  console.log('🚀 التطبيق جاهز');
  createWindow();
});

app.on('window-all-closed', () => {
  console.log('🔚 إغلاق جميع النوافذ');
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  console.log('🔄 تفعيل التطبيق');
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// معالجة الأخطاء غير المتوقعة
process.on('uncaughtException', (error) => {
  console.error('💥 خطأ غير متوقع:', error);
  console.error('📋 Stack trace:', error.stack);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('🚫 رفض غير معالج في:', promise);
  console.error('📋 السبب:', reason);
});

console.log('✅ تم تحميل debug-app.js بنجاح');
