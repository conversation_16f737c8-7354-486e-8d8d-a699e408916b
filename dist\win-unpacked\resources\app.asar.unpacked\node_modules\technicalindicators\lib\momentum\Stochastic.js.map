{"version": 3, "file": "Stochastic.js", "sourceRoot": "", "sources": ["../../src/momentum/Stochastic.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AACnE;;GAEG;AACH,YAAY,CAAA;AAEZ,OAAO,UAAU,MAAM,8BAA8B,CAAC;AACtD,OAAO,EAAE,GAAG,EAAE,MAAO,wBAAwB,CAAC;AAE9C,MAAM,sBAAuB,SAAQ,cAAc;CAMlD;AAAA,CAAC;AAEF,MAAM;CAGL;AAAA,CAAC;AAEF,MAAM,iBAAkB,SAAQ,SAAS;IAGvC,YAAa,KAAqB;QAChC,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;QACtC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,EAAE,CAAA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAE,CAAC,CAAA,CAAC;YACzE,MAAM,CAAC,2CAA2C,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,qEAAqE;QACrE,sBAAsB;QACtB,EAAE;QACF,kDAAkD;QAClD,sDAAsD;QACtD,8DAA8D;QAC9D,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACzB,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,eAAe,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,cAAc,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACzD,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC;gBACjB,MAAM,EAAG,YAAY;gBACrB,MAAM,EAAG,EAAE;gBACX,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC;aAC3B,CAAC,CAAC;YACH,IAAI,CAAC,EAAC,CAAC,CAAC;YACR,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,OAAO,IAAI,EAAE,CAAC;gBACZ,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC9B,EAAE,CAAA,CAAC,KAAK,GAAG,MAAM,CAAC,CAAA,CAAC;oBACjB,KAAK,EAAE,CAAC;oBACR,IAAI,GAAG,KAAK,CAAC;oBACb,QAAQ,CAAC;gBACX,CAAC;gBACD,IAAI,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;gBACzC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,eAAe,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC;gBAC9E,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,wFAAwF;gBAC9G,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,GAAG,MAAM;oBACX,CAAC,EAAG,MAAM,CAAC,CAAC,CAAC;oBACb,CAAC,EAAG,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;iBAC9C,CAAA;YACH,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC3B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAG,KAAK,CAAC,KAAK,CAAC;gBACnB,GAAG,EAAI,IAAI,CAAC,KAAK,CAAC;gBAClB,KAAK,EAAG,MAAM,CAAC,KAAK,CAAC;aACtB,CAAC,CAAC;YACH,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAA,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAAA,CAAC;IAIF,SAAS,CAAE,KAAqB;QAC9B,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,EAAE,CAAA,CAAC,UAAU,CAAC,KAAK,KAAK,SAAS,CAAC;YAChC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;IAC5B,CAAC;IAAA,CAAC;;AANK,oBAAS,GAAG,UAAU,CAAA;AAS/B,MAAM,qBAAqB,KAAqB;IACxC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC1C,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC;AAAA,CAAC"}