// اختبار نهائي للواجهة والتطبيق
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

console.log('🧪 بدء الاختبار النهائي للواجهة...');

let mainWindow;
let testResults = {
    preloadLoaded: false,
    apiExposed: false,
    analyzeMarketAvailable: false,
    analysisWorking: false,
    uiResponsive: false
};

function createWindow() {
    console.log('🪟 إنشاء نافذة الاختبار النهائي...');
    
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js'),
            devTools: true
        },
        title: 'اختبار نهائي - محلل البتكوين الاحترافي',
        show: false
    });

    mainWindow.loadFile('src/index.html');

    mainWindow.once('ready-to-show', () => {
        console.log('✅ النافذة جاهزة');
        mainWindow.show();
        mainWindow.webContents.openDevTools();
        
        // بدء الاختبارات التلقائية
        setTimeout(() => {
            runAutomatedTests();
        }, 2000);
    });

    // تسجيل رسائل من الواجهة
    mainWindow.webContents.on('console-message', (event, level, message) => {
        console.log(`[RENDERER ${level}] ${message}`);
        
        // تتبع حالة التحميل
        if (message.includes('تم تحميل preload.js بنجاح')) {
            testResults.preloadLoaded = true;
        }
        if (message.includes('window.api متوفر')) {
            testResults.apiExposed = true;
        }
        if (message.includes('analyzeMarket')) {
            testResults.analyzeMarketAvailable = true;
        }
    });

    mainWindow.webContents.on('did-finish-load', () => {
        console.log('✅ تم تحميل الصفحة');
    });
}

// معالج تحليل السوق مع تسجيل مفصل
ipcMain.handle('analyze-market', async () => {
    console.log('📡 تم استلام طلب analyze-market من الواجهة');
    
    try {
        const { analyzeMarket } = require('./lib/signals');
        console.log('✅ تم تحميل مكتبة signals');
        
        const startTime = Date.now();
        const analysis = await analyzeMarket();
        const endTime = Date.now();
        
        console.log(`✅ تم إكمال التحليل في ${endTime - startTime}ms`);
        testResults.analysisWorking = true;
        
        return {
            success: true,
            data: analysis
        };
    } catch (error) {
        console.error('❌ خطأ في التحليل:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
});

// معالج فحص الاتصال
ipcMain.handle('check-connection', async () => {
    console.log('🌐 فحص الاتصال...');
    
    try {
        const axios = require('axios');
        await axios.get('https://api.coingecko.com/api/v3/ping', { timeout: 5000 });
        console.log('✅ الاتصال يعمل');
        return { connected: true };
    } catch (error) {
        console.error('❌ فشل الاتصال:', error.message);
        return { connected: false, error: error.message };
    }
});

// اختبارات تلقائية
async function runAutomatedTests() {
    console.log('\n🤖 بدء الاختبارات التلقائية...');
    
    try {
        // اختبار 1: التحقق من توفر API في الواجهة
        console.log('🔍 اختبار 1: فحص توفر API...');
        const apiCheck = await mainWindow.webContents.executeJavaScript(`
            new Promise((resolve) => {
                setTimeout(() => {
                    resolve({
                        apiAvailable: typeof window.api !== 'undefined',
                        analyzeMarketAvailable: typeof window.api?.analyzeMarket === 'function',
                        testFunctionWorks: typeof window.api?.test === 'function' ? window.api.test() : 'غير متوفر'
                    });
                }, 500);
            });
        `);
        
        console.log('📊 نتائج فحص API:', apiCheck);
        
        if (apiCheck.apiAvailable && apiCheck.analyzeMarketAvailable) {
            console.log('✅ API متوفر ويعمل بشكل صحيح');
            testResults.apiExposed = true;
            testResults.analyzeMarketAvailable = true;
        } else {
            console.log('❌ مشكلة في API');
        }
        
        // اختبار 2: محاولة تشغيل التحليل من الواجهة
        if (apiCheck.analyzeMarketAvailable) {
            console.log('🔍 اختبار 2: تشغيل التحليل من الواجهة...');
            
            const analysisResult = await mainWindow.webContents.executeJavaScript(`
                window.api.analyzeMarket().then(result => {
                    return {
                        success: result?.success || false,
                        hasData: !!result?.data,
                        hasRecommendation: !!result?.data?.recommendation,
                        error: result?.error || null
                    };
                }).catch(error => {
                    return {
                        success: false,
                        error: error.message
                    };
                });
            `);
            
            console.log('📊 نتائج التحليل من الواجهة:', analysisResult);
            
            if (analysisResult.success) {
                console.log('✅ التحليل يعمل من الواجهة');
                testResults.analysisWorking = true;
            } else {
                console.log('❌ التحليل لا يعمل من الواجهة:', analysisResult.error);
            }
        }
        
        // اختبار 3: فحص عناصر الواجهة
        console.log('🔍 اختبار 3: فحص عناصر الواجهة...');
        
        const uiElements = await mainWindow.webContents.executeJavaScript(`
            ({
                analyzeBtn: !!document.getElementById('analyzeBtn'),
                startAnalysisBtn: !!document.getElementById('startAnalysisBtn'),
                connectionStatus: !!document.getElementById('connectionStatus'),
                loadingState: !!document.getElementById('loadingState'),
                resultsContainer: !!document.getElementById('resultsContainer'),
                errorState: !!document.getElementById('errorState')
            });
        `);
        
        console.log('📊 عناصر الواجهة:', uiElements);
        
        const allElementsPresent = Object.values(uiElements).every(Boolean);
        if (allElementsPresent) {
            console.log('✅ جميع عناصر الواجهة موجودة');
            testResults.uiResponsive = true;
        } else {
            console.log('❌ بعض عناصر الواجهة مفقودة');
        }
        
        // طباعة التقرير النهائي
        setTimeout(() => {
            printFinalReport();
        }, 2000);
        
    } catch (error) {
        console.error('❌ خطأ في الاختبارات التلقائية:', error);
    }
}

function printFinalReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📋 تقرير الاختبار النهائي للواجهة');
    console.log('='.repeat(60));
    
    console.log('\n🔧 حالة المكونات:');
    console.log(`  preload.js تم تحميله: ${testResults.preloadLoaded ? '✅' : '❌'}`);
    console.log(`  API تم تعريضه: ${testResults.apiExposed ? '✅' : '❌'}`);
    console.log(`  analyzeMarket متوفر: ${testResults.analyzeMarketAvailable ? '✅' : '❌'}`);
    console.log(`  التحليل يعمل: ${testResults.analysisWorking ? '✅' : '❌'}`);
    console.log(`  الواجهة مستجيبة: ${testResults.uiResponsive ? '✅' : '❌'}`);
    
    const allTestsPassed = Object.values(testResults).every(Boolean);
    
    console.log('\n🏆 النتيجة النهائية:');
    if (allTestsPassed) {
        console.log('✅ جميع الاختبارات نجحت! التطبيق جاهز للبناء النهائي.');
        console.log('🚀 يمكن المتابعة لبناء الملفات التنفيذية.');
    } else {
        console.log('⚠️ بعض الاختبارات فشلت. يحتاج إصلاح قبل البناء النهائي.');
        console.log('🔧 راجع الأخطاء أعلاه وأصلحها قبل المتابعة.');
    }
    
    console.log('\n💡 للمتابعة يدوياً:');
    console.log('  - جرب النقر على "حلل الآن" في التطبيق');
    console.log('  - تحقق من عدم ظهور أخطاء في Console');
    console.log('  - تأكد من ظهور النتائج بشكل صحيح');
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// معالجة الأخطاء
process.on('uncaughtException', (error) => {
    console.error('💥 خطأ غير متوقع:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('🚫 رفض غير معالج:', reason);
});

console.log('✅ تم تحميل اختبار الواجهة النهائي');
