@echo off
chcp 65001 >nul
echo.
echo ===============================================
echo    إصلاح مشاكل محلل البتكوين الاحترافي
echo ===============================================
echo.

echo 🔍 تشخيص المشاكل المحتملة...
echo.

:: التحقق من الملفات المطلوبة
echo 📁 التحقق من الملفات الأساسية:
echo.

if exist "main.js" (
    echo ✅ main.js موجود
) else (
    echo ❌ main.js مفقود
    goto :error
)

if exist "preload.js" (
    echo ✅ preload.js موجود
) else (
    echo ❌ preload.js مفقود
    goto :error
)

if exist "src\index.html" (
    echo ✅ src\index.html موجود
) else (
    echo ❌ src\index.html مفقود
    goto :error
)

if exist "src\renderer.js" (
    echo ✅ src\renderer.js موجود
) else (
    echo ❌ src\renderer.js مفقود
    goto :error
)

if exist "lib\signals.js" (
    echo ✅ lib\signals.js موجود
) else (
    echo ❌ lib\signals.js مفقود - هذا قد يكون سبب المشكلة!
    goto :fix_signals
)

echo.
echo 🧪 اختبار المكونات:
echo.

:: اختبار تحليل السوق
echo 📊 اختبار مكتبة التحليل...
node -e "
try {
    const signals = require('./lib/signals');
    console.log('✅ تم تحميل lib/signals بنجاح');
    console.log('📋 الدوال المتوفرة:', Object.keys(signals));
    if (signals.analyzeMarket) {
        console.log('✅ دالة analyzeMarket متوفرة');
    } else {
        console.log('❌ دالة analyzeMarket غير متوفرة');
    }
} catch (error) {
    console.log('❌ خطأ في تحميل lib/signals:', error.message);
}
" 2>nul

echo.
echo 🌐 اختبار الاتصال بالإنترنت...
node -e "
const axios = require('axios');
axios.get('https://api.coingecko.com/api/v3/ping', {timeout: 5000})
    .then(() => console.log('✅ الاتصال بـ CoinGecko يعمل'))
    .catch(err => console.log('❌ مشكلة في الاتصال:', err.message));
" 2>nul

echo.
echo 🔧 الحلول المتاحة:
echo.
echo 1. تشغيل التطبيق في وضع التشخيص
echo 2. إعادة بناء مكتبة التحليل
echo 3. تنظيف وإعادة تثبيت التبعيات
echo 4. اختبار النسخة المبنية
echo 5. عرض سجل الأخطاء
echo 6. خروج
echo.
set /p choice="اختر رقم الحل (1-6): "

if "%choice%"=="1" goto debug_mode
if "%choice%"=="2" goto rebuild_signals
if "%choice%"=="3" goto clean_install
if "%choice%"=="4" goto test_built
if "%choice%"=="5" goto show_logs
if "%choice%"=="6" goto :eof
echo ❌ اختيار غير صحيح
pause
goto :eof

:debug_mode
echo.
echo 🔧 تشغيل التطبيق في وضع التشخيص...
echo.
echo سيتم فتح التطبيق مع أدوات المطور لرؤية الأخطاء بالتفصيل
echo راقب نافذة Console في أدوات المطور
echo.
pause
npm run debug
goto :eof

:rebuild_signals
echo.
echo 🔨 إعادة بناء مكتبة التحليل...
echo.

:: التحقق من وجود المجلد
if not exist "lib" mkdir lib

:: إنشاء ملف signals.js جديد
echo إنشاء lib/signals.js...
node -e "
const fs = require('fs');
const signalsContent = \`
const axios = require('axios');
const { RSI, MACD, EMA, BollingerBands } = require('technicalindicators');

async function analyzeMarket() {
    try {
        console.log('🔍 بدء تحليل السوق...');
        
        // جلب بيانات البتكوين
        const response = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_change=true');
        const price = response.data.bitcoin.usd;
        const change24h = response.data.bitcoin.usd_24h_change;
        
        console.log('💰 السعر الحالي:', price);
        
        // تحليل بسيط
        const analysis = {
            price: price,
            change24h: change24h,
            recommendation: change24h > 0 ? 'شراء' : 'بيع',
            confidence: Math.abs(change24h) > 5 ? 'عالي' : 'متوسط',
            timestamp: new Date().toISOString()
        };
        
        console.log('✅ تم إكمال التحليل');
        return analysis;
        
    } catch (error) {
        console.error('❌ خطأ في التحليل:', error.message);
        throw error;
    }
}

module.exports = { analyzeMarket };
\`;

fs.writeFileSync('lib/signals.js', signalsContent);
console.log('✅ تم إنشاء lib/signals.js بنجاح');
"

echo ✅ تم إعادة بناء مكتبة التحليل
echo.
echo جرب تشغيل التطبيق الآن
pause
goto :eof

:clean_install
echo.
echo 🧹 تنظيف وإعادة تثبيت التبعيات...
echo.

echo حذف node_modules...
if exist "node_modules" rmdir /s /q "node_modules"

echo حذف package-lock.json...
if exist "package-lock.json" del "package-lock.json"

echo إعادة تثبيت التبعيات...
npm install

echo ✅ تم تنظيف وإعادة تثبيت التبعيات
pause
goto :eof

:test_built
echo.
echo 🧪 اختبار النسخة المبنية...
echo.

if exist "dist\win-unpacked\Bitcoin Analyzer Pro.exe" (
    echo تشغيل النسخة المبنية...
    start "" "dist\win-unpacked\Bitcoin Analyzer Pro.exe"
    echo ✅ تم تشغيل النسخة المبنية
) else (
    echo ❌ النسخة المبنية غير موجودة
    echo يرجى بناء التطبيق أولاً باستخدام: npm run build:win
)
pause
goto :eof

:show_logs
echo.
echo 📋 عرض سجل الأخطاء...
echo.

echo تشغيل التطبيق مع تسجيل مفصل...
echo راقب هذه النافذة للأخطاء:
echo.

npm start 2>&1 | findstr /i "error\|خطأ\|failed\|فشل"

pause
goto :eof

:fix_signals
echo.
echo 🔨 إصلاح مكتبة signals المفقودة...
echo.

if not exist "lib" mkdir lib

echo إنشاء lib/signals.js...
node -e "
const fs = require('fs');
const signalsContent = \`const axios = require('axios');

async function analyzeMarket() {
    try {
        const response = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_change=true');
        const price = response.data.bitcoin.usd;
        const change24h = response.data.bitcoin.usd_24h_change;
        
        return {
            price: price,
            change24h: change24h,
            recommendation: change24h > 0 ? 'شراء' : 'بيع',
            confidence: 'متوسط',
            timestamp: new Date().toISOString()
        };
    } catch (error) {
        throw error;
    }
}

module.exports = { analyzeMarket };\`;

fs.writeFileSync('lib/signals.js', signalsContent);
console.log('✅ تم إنشاء lib/signals.js');
"

echo ✅ تم إصلاح مكتبة signals
echo جرب تشغيل التطبيق الآن
pause
goto :eof

:error
echo.
echo ❌ توجد ملفات مفقودة مهمة
echo يرجى التأكد من وجود جميع ملفات المشروع
pause
goto :eof
