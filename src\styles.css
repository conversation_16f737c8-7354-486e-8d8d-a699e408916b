/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
    direction: rtl;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.title {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.bitcoin-icon {
    color: #f39c12;
    font-size: 2.5rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Live Price Display */
.live-price-display {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    min-width: 200px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.live-price-display.updating {
    border-color: #3498db;
    box-shadow: 0 4px 20px rgba(52, 152, 219, 0.3);
}

.live-price-label {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-bottom: 0.25rem;
}

.live-price-value {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    font-family: 'Courier New', monospace;
}

.live-price-change {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.live-price-change.positive {
    color: #27ae60;
}

.live-price-change.negative {
    color: #e74c3c;
}

.live-price-change.neutral {
    color: #95a5a6;
}

.live-price-update {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    opacity: 0.7;
}

.update-indicator {
    font-size: 0.6rem;
    transition: color 0.3s ease;
}

.update-indicator.active {
    color: #3498db;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

.analyze-btn, .start-analysis-btn {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.analyze-btn:hover, .start-analysis-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.analyze-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #95a5a6;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #27ae60;
}

.status-dot.disconnected {
    background: #e74c3c;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 4rem 2rem;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(52, 152, 219, 0.3);
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1.2rem;
    color: white;
    margin-bottom: 2rem;
}

.loading-steps {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.step {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    color: white;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.step.active {
    background: rgba(52, 152, 219, 0.8);
    transform: scale(1.05);
}

.step.completed {
    background: rgba(39, 174, 96, 0.8);
}

/* Results Container */
.results-container {
    display: grid;
    gap: 2rem;
}

/* Price Overview */
.price-overview {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.price-card, .recommendation-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.price-card h2, .recommendation-card h2 {
    font-size: 1.2rem;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.price-value {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.price-change {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.price-change.positive {
    color: #27ae60;
}

.price-change.negative {
    color: #e74c3c;
}

.last-update {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.recommendation-badge {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 1.2rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
}

.recommendation-badge.buy {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
}

.recommendation-badge.sell {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
}

.recommendation-badge.hold {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.confidence-level, .risk-level {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 0.5rem;
}

/* Forecast Section */
.forecast-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.forecast-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.forecast-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.forecast-card {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.forecast-card:hover {
    border-color: #3498db;
    transform: translateY(-2px);
}

.forecast-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.forecast-price {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.forecast-change {
    font-size: 1rem;
    font-weight: 600;
}

.forecast-change.positive {
    color: #27ae60;
}

.forecast-change.negative {
    color: #e74c3c;
}

/* Technical Indicators */
.indicators-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.indicators-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.indicators-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.indicator-card {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.indicator-card:hover {
    border-color: #3498db;
    transform: translateY(-2px);
}

.indicator-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.indicator-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.indicator-signal {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    display: inline-block;
}

.indicator-signal.bullish {
    background: rgba(39, 174, 96, 0.2);
    color: #27ae60;
}

.indicator-signal.bearish {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.indicator-signal.neutral {
    background: rgba(149, 165, 166, 0.2);
    color: #95a5a6;
}

.indicator-bar {
    width: 100%;
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    margin-top: 0.5rem;
    overflow: hidden;
}

.indicator-fill {
    height: 100%;
    background: linear-gradient(90deg, #e74c3c, #f39c12, #27ae60);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Chart Section */
.chart-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.chart-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}

/* Analysis Section */
.analysis-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.analysis-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.analysis-content {
    display: grid;
    gap: 2rem;
}

.analysis-summary, .analysis-reasons, .patterns-detected {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
}

.analysis-summary h3, .analysis-reasons h3, .patterns-detected h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.analysis-summary p {
    line-height: 1.6;
    color: #2c3e50;
}

.analysis-reasons ul {
    list-style: none;
}

.analysis-reasons li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    color: #2c3e50;
}

.analysis-reasons li:last-child {
    border-bottom: none;
}

.analysis-reasons li::before {
    content: "✓";
    color: #27ae60;
    font-weight: bold;
    margin-left: 0.5rem;
}

/* Error and Welcome States */
.error-state, .welcome-state {
    text-align: center;
    padding: 4rem 2rem;
    color: white;
}

.error-icon, .welcome-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.error-state h2, .welcome-state h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.error-state p, .welcome-state p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.retry-btn {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.features-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.feature-icon {
    font-size: 1.5rem;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 2rem;
    margin-top: auto;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .title {
        font-size: 1.5rem;
    }
    
    .price-overview {
        grid-template-columns: 1fr;
    }
    
    .forecast-cards {
        grid-template-columns: 1fr;
    }
    
    .indicators-grid {
        grid-template-columns: 1fr;
    }
    
    .features-list {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .main-content {
        padding: 1rem;
    }
}
