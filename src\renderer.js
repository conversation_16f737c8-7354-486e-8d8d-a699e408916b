// Global variables
let currentAnalysis = null;
let priceChart = null;
let livePriceInterval = null;
let lastPrice = null;

// DOM Elements
const elements = {
    analyzeBtn: document.getElementById('analyzeBtn'),
    startAnalysisBtn: document.getElementById('startAnalysisBtn'),
    retryBtn: document.getElementById('retryBtn'),
    connectionStatus: document.getElementById('connectionStatus'),
    loadingState: document.getElementById('loadingState'),
    resultsContainer: document.getElementById('resultsContainer'),
    errorState: document.getElementById('errorState'),
    welcomeState: document.getElementById('welcomeState'),
    
    // Price elements
    currentPrice: document.getElementById('currentPrice'),
    priceChange: document.getElementById('priceChange'),
    lastUpdate: document.getElementById('lastUpdate'),
    
    // Recommendation elements
    recommendationBadge: document.getElementById('recommendationBadge'),
    confidenceLevel: document.getElementById('confidenceLevel'),
    riskLevel: document.getElementById('riskLevel'),
    
    // Forecast elements
    forecast24h: document.getElementById('forecast24h'),
    change24h: document.getElementById('change24h'),
    forecast48h: document.getElementById('forecast48h'),
    change48h: document.getElementById('change48h'),
    
    // Indicator elements
    rsiValue: document.getElementById('rsiValue'),
    rsiFill: document.getElementById('rsiFill'),
    macdValue: document.getElementById('macdValue'),
    macdSignal: document.getElementById('macdSignal'),
    emaValue: document.getElementById('emaValue'),
    emaSignal: document.getElementById('emaSignal'),
    bbValue: document.getElementById('bbValue'),
    bbSignal: document.getElementById('bbSignal'),
    stochValue: document.getElementById('stochValue'),
    stochSignal: document.getElementById('stochSignal'),
    adxValue: document.getElementById('adxValue'),
    adxSignal: document.getElementById('adxSignal'),
    
    // Analysis elements
    summaryText: document.getElementById('summaryText'),
    reasonsList: document.getElementById('reasonsList'),
    patternsList: document.getElementById('patternsList'),
    
    // Footer elements
    appVersion: document.getElementById('appVersion'),
    lastAnalysisTime: document.getElementById('lastAnalysisTime'),

    // Live price elements
    livePriceDisplay: document.getElementById('livePriceDisplay'),
    livePriceValue: document.getElementById('livePriceValue'),
    livePriceChange: document.getElementById('livePriceChange'),
    livePriceUpdate: document.getElementById('livePriceUpdate'),
    updateIndicator: document.getElementById('updateIndicator'),
    updateTime: document.getElementById('updateTime')
};

// Initialize the application
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 تطبيق محلل البتكوين جاهز');

    // التحقق من توفر window.api
    if (!window.api) {
        console.error('❌ window.api غير متوفر! تحقق من preload.js');
        showErrorState('خطأ في تحميل التطبيق. يرجى إعادة تشغيل التطبيق.');
        return;
    }

    console.log('✅ window.api متوفر:', Object.keys(window.api));

    // اختبار API
    try {
        if (window.api.test) {
            const testResult = window.api.test();
            console.log('🧪 اختبار API:', testResult);
        }
    } catch (error) {
        console.error('❌ خطأ في اختبار API:', error);
    }

    // Set app version
    if (window.api.version) {
        elements.appVersion.textContent = window.api.version;
        console.log('📱 إصدار التطبيق:', window.api.version);
    }

    // Check connection status
    console.log('🌐 فحص حالة الاتصال...');
    await checkConnection();

    // Setup event listeners
    console.log('🔧 إعداد مستمعي الأحداث...');
    setupEventListeners();

    // Show welcome state initially
    console.log('🏠 عرض الشاشة الرئيسية...');
    showWelcomeState();

    // Start live price updates
    console.log('💰 بدء تحديث السعر المباشر...');
    startLivePriceUpdates();

    console.log('✅ تم تهيئة التطبيق بنجاح');
});

// Setup event listeners
function setupEventListeners() {
    elements.analyzeBtn.addEventListener('click', startAnalysis);
    elements.startAnalysisBtn.addEventListener('click', startAnalysis);
    elements.retryBtn.addEventListener('click', startAnalysis);
    
    // Auto-refresh connection status every 30 seconds
    setInterval(checkConnection, 30000);
}

// Check internet connection
async function checkConnection() {
    try {
        const result = await window.api.checkConnection();
        updateConnectionStatus(result.connected);
    } catch (error) {
        console.error('خطأ في فحص الاتصال:', error);
        updateConnectionStatus(false);
    }
}

// Update connection status UI
function updateConnectionStatus(connected) {
    const statusDot = elements.connectionStatus.querySelector('.status-dot');
    const statusText = elements.connectionStatus.querySelector('.status-text');
    
    if (connected) {
        statusDot.className = 'status-dot connected';
        statusText.textContent = 'متصل';
        elements.analyzeBtn.disabled = false;
    } else {
        statusDot.className = 'status-dot disconnected';
        statusText.textContent = 'غير متصل';
        elements.analyzeBtn.disabled = true;
    }
}

// Start market analysis
async function startAnalysis() {
    try {
        console.log('بدء التحليل...');
        
        // Show loading state
        showLoadingState();
        
        // Disable analyze button
        elements.analyzeBtn.disabled = true;
        
        // التحقق من توفر API
        if (!window.api) {
            throw new Error('API غير متوفر. يرجى إعادة تشغيل التطبيق.');
        }

        if (!window.api.analyzeMarket) {
            throw new Error('دالة analyzeMarket غير متوفرة. تحقق من إعدادات التطبيق.');
        }

        console.log('📡 استدعاء analyzeMarket...');

        // Start analysis with progress tracking
        const result = await window.api.analyzeMarket();

        console.log('📊 نتيجة التحليل:', result ? 'تم استلام البيانات' : 'لا توجد بيانات');
        
        if (result.success) {
            currentAnalysis = result.data;
            displayResults(currentAnalysis);
            showResultsState();
            
            // Update last analysis time
            elements.lastAnalysisTime.textContent = new Date().toLocaleString('ar-SA');
            
            console.log('تم إكمال التحليل بنجاح');
        } else {
            throw new Error(result.error || 'فشل في التحليل');
        }
        
    } catch (error) {
        console.error('خطأ في التحليل:', error);
        showErrorState(error.message);
    } finally {
        // Re-enable analyze button
        elements.analyzeBtn.disabled = false;
    }
}

// Show different states
function showWelcomeState() {
    hideAllStates();
    elements.welcomeState.classList.remove('hidden');
}

function showLoadingState() {
    hideAllStates();
    elements.loadingState.classList.remove('hidden');
    
    // Animate loading steps
    animateLoadingSteps();
}

function showResultsState() {
    console.log('📊 عرض حالة النتائج...');
    hideAllStates();

    if (elements.resultsContainer) {
        elements.resultsContainer.classList.remove('hidden');
        console.log('✅ تم إظهار حاوية النتائج');

        // Ensure chart container is visible
        const chartSection = document.querySelector('.chart-section');
        if (chartSection) {
            chartSection.style.display = 'block';
            console.log('✅ تم إظهار قسم الرسم البياني');
        }

        // Ensure indicators section is visible
        const indicatorsSection = document.querySelector('.indicators-section');
        if (indicatorsSection) {
            indicatorsSection.style.display = 'block';
            console.log('✅ تم إظهار قسم المؤشرات');
        }
    } else {
        console.error('❌ حاوية النتائج غير موجودة');
    }
}

function showErrorState(message) {
    hideAllStates();
    elements.errorState.classList.remove('hidden');
    document.getElementById('errorMessage').textContent = message;
}

function hideAllStates() {
    elements.loadingState.classList.add('hidden');
    elements.resultsContainer.classList.add('hidden');
    elements.errorState.classList.add('hidden');
    elements.welcomeState.classList.add('hidden');
}

// Animate loading steps
function animateLoadingSteps() {
    const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];
    const stepNames = [
        'جلب البيانات',
        'حساب المؤشرات',
        'تحليل الأنماط',
        'التنبؤ السعري',
        'إعداد التوصية'
    ];
    
    steps.forEach((stepId, index) => {
        setTimeout(() => {
            const stepElement = document.getElementById(stepId);
            if (stepElement) {
                stepElement.classList.add('active');
                
                // Mark previous steps as completed
                steps.slice(0, index).forEach(prevStepId => {
                    const prevStep = document.getElementById(prevStepId);
                    if (prevStep) {
                        prevStep.classList.remove('active');
                        prevStep.classList.add('completed');
                    }
                });
            }
        }, index * 1000);
    });
}

// Display analysis results
function displayResults(analysis) {
    try {
        // Update market data
        updateMarketData(analysis.marketData);
        
        // Update recommendation
        updateRecommendation(analysis.recommendation);
        
        // Update forecast
        updateForecast(analysis.forecast);
        
        // Update technical indicators
        console.log('📈 تحديث المؤشرات...');
        updateIndicators(analysis.indicators);

        // Update analysis details
        console.log('📋 تحديث تفاصيل التحليل...');
        updateAnalysisDetails(analysis);

        // Update chart with delay to ensure DOM is ready
        console.log('📊 تحديث الرسم البياني...');
        setTimeout(() => {
            updateChart(analysis.marketData);
        }, 100);

        console.log('✅ تم عرض جميع النتائج بنجاح');

    } catch (error) {
        console.error('❌ خطأ في عرض النتائج:', error);
        console.error('📋 تفاصيل الخطأ:', error.stack);
    }
}

// Update market data display
function updateMarketData(marketData) {
    if (!marketData) return;
    
    // Current price
    elements.currentPrice.textContent = `$${marketData.currentPrice.toLocaleString()}`;
    
    // Price change
    const change = marketData.change24h;
    elements.priceChange.textContent = `${change > 0 ? '+' : ''}${change.toFixed(2)}%`;
    elements.priceChange.className = `price-change ${change > 0 ? 'positive' : 'negative'}`;
    
    // Last update
    elements.lastUpdate.textContent = `آخر تحديث: ${new Date(marketData.lastUpdate).toLocaleString('ar-SA')}`;
}

// Update recommendation display
function updateRecommendation(recommendation) {
    if (!recommendation) return;
    
    // Recommendation badge
    const decision = recommendation.decision;
    elements.recommendationBadge.textContent = decision;
    
    let badgeClass = 'recommendation-badge';
    if (decision === 'شراء') badgeClass += ' buy';
    else if (decision === 'بيع') badgeClass += ' sell';
    else badgeClass += ' hold';
    
    elements.recommendationBadge.className = badgeClass;
    
    // Confidence and risk levels
    elements.confidenceLevel.textContent = `مستوى الثقة: ${recommendation.confidence}%`;
    elements.riskLevel.textContent = `مستوى المخاطرة: ${recommendation.riskLevel}`;
}

// Update forecast display
function updateForecast(forecast) {
    if (!forecast || !forecast.combined) return;
    
    const combined = forecast.combined;
    const currentPrice = currentAnalysis?.marketData?.currentPrice || 0;
    
    // 24h forecast
    if (combined.next24h) {
        elements.forecast24h.textContent = `$${combined.next24h.toLocaleString()}`;
        const change24h = ((combined.next24h - currentPrice) / currentPrice) * 100;
        elements.change24h.textContent = `${change24h > 0 ? '+' : ''}${change24h.toFixed(2)}%`;
        elements.change24h.className = `forecast-change ${change24h > 0 ? 'positive' : 'negative'}`;
    }
    
    // 48h forecast
    if (combined.next48h) {
        elements.forecast48h.textContent = `$${combined.next48h.toLocaleString()}`;
        const change48h = ((combined.next48h - currentPrice) / currentPrice) * 100;
        elements.change48h.textContent = `${change48h > 0 ? '+' : ''}${change48h.toFixed(2)}%`;
        elements.change48h.className = `forecast-change ${change48h > 0 ? 'positive' : 'negative'}`;
    }
}

// Update technical indicators display
function updateIndicators(indicators) {
    if (!indicators) return;
    
    // RSI
    if (indicators.currentRSI !== null) {
        elements.rsiValue.textContent = indicators.currentRSI.toFixed(1);
        elements.rsiFill.style.width = `${indicators.currentRSI}%`;
    }
    
    // MACD
    if (indicators.currentMACD) {
        elements.macdValue.textContent = indicators.currentMACD.macd.toFixed(4);
        const signal = indicators.currentMACD.histogram > 0 ? 'صعودي' : 'هبوطي';
        elements.macdSignal.textContent = signal;
        elements.macdSignal.className = `indicator-signal ${indicators.currentMACD.histogram > 0 ? 'bullish' : 'bearish'}`;
    }
    
    // EMA
    if (indicators.currentEMA50 && indicators.currentEMA200) {
        elements.emaValue.textContent = `${indicators.currentEMA50.toFixed(0)} / ${indicators.currentEMA200.toFixed(0)}`;
        const signal = indicators.currentEMA50 > indicators.currentEMA200 ? 'صعودي' : 'هبوطي';
        elements.emaSignal.textContent = signal;
        elements.emaSignal.className = `indicator-signal ${indicators.currentEMA50 > indicators.currentEMA200 ? 'bullish' : 'bearish'}`;
    }
    
    // Bollinger Bands
    if (indicators.currentBB) {
        elements.bbValue.textContent = `${indicators.currentBB.upper.toFixed(0)} / ${indicators.currentBB.lower.toFixed(0)}`;
        const position = indicators.currentBB.position;
        let signal = 'متوسط';
        let signalClass = 'neutral';
        
        if (position === 'above_upper' || position === 'near_upper') {
            signal = 'ذروة شراء';
            signalClass = 'bearish';
        } else if (position === 'below_lower' || position === 'near_lower') {
            signal = 'ذروة بيع';
            signalClass = 'bullish';
        }
        
        elements.bbSignal.textContent = signal;
        elements.bbSignal.className = `indicator-signal ${signalClass}`;
    }
    
    // Stochastic
    if (indicators.currentStochastic) {
        elements.stochValue.textContent = `${indicators.currentStochastic.k.toFixed(1)} / ${indicators.currentStochastic.d.toFixed(1)}`;
        const signal = indicators.currentStochastic.k > 80 ? 'ذروة شراء' : 
                     indicators.currentStochastic.k < 20 ? 'ذروة بيع' : 'متوسط';
        const signalClass = indicators.currentStochastic.k > 80 ? 'bearish' : 
                           indicators.currentStochastic.k < 20 ? 'bullish' : 'neutral';
        elements.stochSignal.textContent = signal;
        elements.stochSignal.className = `indicator-signal ${signalClass}`;
    }
    
    // ADX
    if (indicators.currentADX !== null) {
        elements.adxValue.textContent = indicators.currentADX.toFixed(1);
        const signal = indicators.currentADX > 25 ? 'اتجاه قوي' : 'اتجاه ضعيف';
        elements.adxSignal.textContent = signal;
        elements.adxSignal.className = `indicator-signal ${indicators.currentADX > 25 ? 'bullish' : 'neutral'}`;
    }
}

// Update analysis details
function updateAnalysisDetails(analysis) {
    // Summary
    if (analysis.recommendation && analysis.recommendation.summary) {
        elements.summaryText.textContent = analysis.recommendation.summary;
    }
    
    // Reasons
    if (analysis.recommendation && analysis.recommendation.reasons) {
        elements.reasonsList.innerHTML = '';
        analysis.recommendation.reasons.forEach(reason => {
            const li = document.createElement('li');
            li.textContent = reason;
            elements.reasonsList.appendChild(li);
        });
    }
    
    // Patterns
    if (analysis.patterns) {
        elements.patternsList.innerHTML = '';
        
        const allPatterns = [
            ...(analysis.patterns.candlestickPatterns || []),
            ...(analysis.patterns.chartPatterns || [])
        ];
        
        if (allPatterns.length > 0) {
            allPatterns.forEach(pattern => {
                const patternDiv = document.createElement('div');
                patternDiv.className = 'pattern-item';
                patternDiv.innerHTML = `
                    <strong>${pattern.type}</strong>
                    <span class="pattern-signal ${pattern.signal}">${pattern.signal === 'bullish' ? 'صعودي' : pattern.signal === 'bearish' ? 'هبوطي' : 'محايد'}</span>
                    <p>${pattern.description}</p>
                `;
                elements.patternsList.appendChild(patternDiv);
            });
        } else {
            elements.patternsList.innerHTML = '<p>لم يتم اكتشاف أنماط واضحة</p>';
        }
    }
}

// Update price chart
function updateChart(marketData) {
    try {
        console.log('📊 بدء تحديث الرسم البياني...', marketData);

        if (!marketData || !marketData.historicalData) {
            console.error('❌ لا توجد بيانات للرسم البياني');
            return;
        }

        const chartElement = document.getElementById('priceChart');
        if (!chartElement) {
            console.error('❌ عنصر الرسم البياني غير موجود');
            return;
        }

        const ctx = chartElement.getContext('2d');

        // Destroy existing chart
        if (priceChart) {
            console.log('🗑️ حذف الرسم البياني السابق...');
            priceChart.destroy();
            priceChart = null;
        }

        // Prepare chart data
        console.log('📈 تحضير بيانات الرسم البياني...', marketData.historicalData.length, 'نقطة');
        const chartData = marketData.historicalData.map(candle => ({
            x: new Date(candle.timestamp),
            y: candle.close
        }));

        console.log('📊 إنشاء رسم بياني جديد...');

        // Create new chart
        priceChart = new Chart(ctx, {
        type: 'line',
        data: {
            datasets: [{
                label: 'سعر البتكوين (USD)',
                data: chartData,
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'hour',
                        displayFormats: {
                            hour: 'MMM dd HH:mm'
                        }
                    },
                    title: {
                        display: true,
                        text: 'الوقت'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'السعر (USD)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    console.log('✅ تم إنشاء الرسم البياني بنجاح');

    } catch (error) {
        console.error('❌ خطأ في إنشاء الرسم البياني:', error);
        console.error('📋 تفاصيل الخطأ:', error.stack);
    }
}

// Live Price Updates
async function startLivePriceUpdates() {
    console.log('🔄 بدء تحديث السعر المباشر...');

    // Update immediately
    await updateLivePrice();

    // Set interval for updates every 2 seconds
    livePriceInterval = setInterval(async () => {
        await updateLivePrice();
    }, 2000);

    console.log('✅ تم بدء تحديث السعر المباشر');
}

async function updateLivePrice() {
    try {
        // Show update indicator
        if (elements.updateIndicator) {
            elements.updateIndicator.classList.add('active');
            elements.livePriceDisplay.classList.add('updating');
        }

        // Fetch current price from CoinGecko
        const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd&include_24hr_change=true');
        const data = await response.json();

        if (data.bitcoin) {
            const currentPrice = data.bitcoin.usd;
            const change24h = data.bitcoin.usd_24h_change;

            // Update price display
            if (elements.livePriceValue) {
                elements.livePriceValue.textContent = `$${currentPrice.toLocaleString('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                })}`;
            }

            // Update change percentage
            if (elements.livePriceChange) {
                const changeText = `${change24h >= 0 ? '+' : ''}${change24h.toFixed(2)}%`;
                elements.livePriceChange.textContent = changeText;

                // Update color based on change
                elements.livePriceChange.className = 'live-price-change';
                if (change24h > 0) {
                    elements.livePriceChange.classList.add('positive');
                } else if (change24h < 0) {
                    elements.livePriceChange.classList.add('negative');
                } else {
                    elements.livePriceChange.classList.add('neutral');
                }
            }

            // Update timestamp
            if (elements.updateTime) {
                const now = new Date();
                elements.updateTime.textContent = now.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            }

            // Store last price for comparison
            lastPrice = currentPrice;

            console.log(`💰 تم تحديث السعر: $${currentPrice} (${change24h.toFixed(2)}%)`);
        }

    } catch (error) {
        console.error('❌ خطأ في تحديث السعر المباشر:', error);

        // Show error in price display
        if (elements.livePriceValue) {
            elements.livePriceValue.textContent = 'خطأ في التحديث';
        }
        if (elements.livePriceChange) {
            elements.livePriceChange.textContent = '--';
            elements.livePriceChange.className = 'live-price-change neutral';
        }
    } finally {
        // Hide update indicator
        setTimeout(() => {
            if (elements.updateIndicator) {
                elements.updateIndicator.classList.remove('active');
                elements.livePriceDisplay.classList.remove('updating');
            }
        }, 500);
    }
}

function stopLivePriceUpdates() {
    if (livePriceInterval) {
        clearInterval(livePriceInterval);
        livePriceInterval = null;
        console.log('⏹️ تم إيقاف تحديث السعر المباشر');
    }
}

// Error handling
window.addEventListener('error', (event) => {
    console.error('خطأ في التطبيق:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('رفض غير معالج:', event.reason);
});

// Stop live updates when page is hidden or unloaded
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        stopLivePriceUpdates();
    } else {
        startLivePriceUpdates();
    }
});

window.addEventListener('beforeunload', () => {
    stopLivePriceUpdates();
});
