// اختبار بسيط لـ API
console.log('🧪 اختبار API محلل البتكوين...');

async function testAPI() {
    try {
        // اختبار تحميل المكتبات
        console.log('📚 اختبار تحميل المكتبات...');
        
        const signals = require('./lib/signals');
        console.log('✅ تم تحميل lib/signals');
        
        if (signals.analyzeMarket) {
            console.log('✅ دالة analyzeMarket متوفرة');
        } else {
            console.log('❌ دالة analyzeMarket غير متوفرة');
            return;
        }
        
        // اختبار الاتصال
        console.log('🌐 اختبار الاتصال...');
        const axios = require('axios');
        
        try {
            await axios.get('https://api.coingecko.com/api/v3/ping', { timeout: 5000 });
            console.log('✅ الاتصال بـ CoinGecko يعمل');
        } catch (error) {
            console.log('❌ مشكلة في الاتصال:', error.message);
            return;
        }
        
        // اختبار التحليل
        console.log('📊 اختبار التحليل...');
        
        const startTime = Date.now();
        const result = await signals.analyzeMarket();
        const endTime = Date.now();
        
        console.log('✅ تم إكمال التحليل في', (endTime - startTime), 'ms');
        console.log('📋 نوع النتيجة:', typeof result);
        
        if (result) {
            console.log('📊 مفاتيح النتيجة:', Object.keys(result));
            
            if (result.currentPrice) {
                console.log('💰 السعر الحالي:', result.currentPrice);
            }
            
            if (result.recommendation) {
                console.log('🎯 التوصية:', result.recommendation);
            }
            
            console.log('✅ التحليل يعمل بشكل صحيح!');
        } else {
            console.log('❌ لا توجد نتائج من التحليل');
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error('📋 تفاصيل الخطأ:', error.stack);
    }
}

// تشغيل الاختبار
testAPI().then(() => {
    console.log('🏁 انتهى الاختبار');
    process.exit(0);
}).catch(error => {
    console.error('💥 فشل الاختبار:', error);
    process.exit(1);
});
