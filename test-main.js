// ملف main.js مبسط للاختبار
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

console.log('🚀 بدء تطبيق الاختبار...');

let mainWindow;

function createWindow() {
  console.log('🪟 إنشاء نافذة الاختبار...');
  
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      devTools: true
    },
    title: 'اختبار API - محلل البتكوين',
    show: false
  });

  console.log('📁 مسار preload:', path.join(__dirname, 'preload.js'));
  console.log('📁 مسار HTML:', path.join(__dirname, 'test-ui.html'));

  mainWindow.loadFile('test-ui.html');

  mainWindow.once('ready-to-show', () => {
    console.log('✅ النافذة جاهزة');
    mainWindow.show();
    mainWindow.webContents.openDevTools();
  });

  // تسجيل الأحداث
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('✅ تم تحميل الصفحة');
  });

  mainWindow.webContents.on('console-message', (event, level, message) => {
    console.log(`[RENDERER] ${message}`);
  });
}

// معالج تحليل السوق
ipcMain.handle('analyze-market', async () => {
  console.log('📡 تم استلام طلب analyze-market');
  
  try {
    const { analyzeMarket } = require('./lib/signals');
    console.log('✅ تم تحميل مكتبة signals');
    
    const analysis = await analyzeMarket();
    console.log('✅ تم إكمال التحليل');
    
    return {
      success: true,
      data: analysis
    };
  } catch (error) {
    console.error('❌ خطأ في التحليل:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
});

// معالج فحص الاتصال
ipcMain.handle('check-connection', async () => {
  console.log('🌐 فحص الاتصال...');
  
  try {
    const axios = require('axios');
    await axios.get('https://api.coingecko.com/api/v3/ping', { timeout: 5000 });
    console.log('✅ الاتصال يعمل');
    return { connected: true };
  } catch (error) {
    console.error('❌ فشل الاتصال:', error.message);
    return { connected: false, error: error.message };
  }
});

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

console.log('✅ تم تحميل test-main.js');
