// اختبار الإصلاحات الجديدة
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

console.log('🧪 اختبار الإصلاحات الجديدة...');

let mainWindow;

function createWindow() {
    console.log('🪟 إنشاء نافذة الاختبار...');
    
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js'),
            devTools: true
        },
        title: 'اختبار الإصلاحات - محلل البتكوين',
        show: false
    });

    mainWindow.loadFile('src/index.html');

    mainWindow.once('ready-to-show', () => {
        console.log('✅ النافذة جاهزة');
        mainWindow.show();
        mainWindow.webContents.openDevTools();
        
        // اختبار تلقائي بعد 3 ثوان
        setTimeout(() => {
            runAutomatedTest();
        }, 3000);
    });

    // تسجيل رسائل من الواجهة
    mainWindow.webContents.on('console-message', (event, level, message) => {
        console.log(`[RENDERER ${level}] ${message}`);
    });
}

// معالج تحليل السوق
ipcMain.handle('analyze-market', async () => {
    console.log('📡 تم استلام طلب analyze-market');
    
    try {
        const { analyzeMarket } = require('./lib/signals');
        console.log('✅ تم تحميل مكتبة signals');
        
        const analysis = await analyzeMarket();
        console.log('✅ تم إكمال التحليل');
        
        return {
            success: true,
            data: analysis
        };
    } catch (error) {
        console.error('❌ خطأ في التحليل:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
});

// معالج فحص الاتصال
ipcMain.handle('check-connection', async () => {
    console.log('🌐 فحص الاتصال...');
    
    try {
        const axios = require('axios');
        await axios.get('https://api.coingecko.com/api/v3/ping', { timeout: 5000 });
        console.log('✅ الاتصال يعمل');
        return { connected: true };
    } catch (error) {
        console.error('❌ فشل الاتصال:', error.message);
        return { connected: false, error: error.message };
    }
});

// اختبار تلقائي
async function runAutomatedTest() {
    console.log('\n🤖 بدء الاختبار التلقائي...');
    
    try {
        // اختبار 1: فحص السعر المباشر
        console.log('💰 اختبار 1: فحص السعر المباشر...');
        
        const livePriceTest = await mainWindow.webContents.executeJavaScript(`
            new Promise((resolve) => {
                setTimeout(() => {
                    const livePriceValue = document.getElementById('livePriceValue');
                    const livePriceChange = document.getElementById('livePriceChange');
                    const updateTime = document.getElementById('updateTime');
                    
                    resolve({
                        priceDisplayed: livePriceValue ? livePriceValue.textContent !== '$--' : false,
                        changeDisplayed: livePriceChange ? livePriceChange.textContent !== '--%' : false,
                        timeDisplayed: updateTime ? updateTime.textContent !== '--:--' : false,
                        priceValue: livePriceValue ? livePriceValue.textContent : 'غير موجود',
                        changeValue: livePriceChange ? livePriceChange.textContent : 'غير موجود'
                    });
                }, 5000); // انتظار 5 ثوان للسعر المباشر
            });
        `);
        
        console.log('📊 نتائج اختبار السعر المباشر:', livePriceTest);
        
        // اختبار 2: تشغيل التحليل واختبار الرسم البياني
        console.log('📈 اختبار 2: تشغيل التحليل...');
        
        const analysisTest = await mainWindow.webContents.executeJavaScript(`
            new Promise(async (resolve) => {
                try {
                    // النقر على زر التحليل
                    const analyzeBtn = document.getElementById('analyzeBtn');
                    if (analyzeBtn) {
                        analyzeBtn.click();
                        console.log('تم النقر على زر التحليل');
                        
                        // انتظار 10 ثوان لإكمال التحليل
                        setTimeout(() => {
                            const resultsContainer = document.getElementById('resultsContainer');
                            const priceChart = document.getElementById('priceChart');
                            const chartSection = document.querySelector('.chart-section');
                            const indicatorsSection = document.querySelector('.indicators-section');
                            
                            resolve({
                                resultsVisible: resultsContainer ? !resultsContainer.classList.contains('hidden') : false,
                                chartExists: !!priceChart,
                                chartSectionVisible: chartSection ? chartSection.style.display !== 'none' : false,
                                indicatorsSectionVisible: indicatorsSection ? indicatorsSection.style.display !== 'none' : false,
                                chartCanvas: priceChart ? priceChart.getContext ? true : false : false
                            });
                        }, 10000);
                    } else {
                        resolve({ error: 'زر التحليل غير موجود' });
                    }
                } catch (error) {
                    resolve({ error: error.message });
                }
            });
        `);
        
        console.log('📊 نتائج اختبار التحليل والرسم البياني:', analysisTest);
        
        // اختبار 3: فحص المؤشرات الفنية
        console.log('📈 اختبار 3: فحص المؤشرات الفنية...');
        
        const indicatorsTest = await mainWindow.webContents.executeJavaScript(`
            ({
                rsiValue: document.getElementById('rsiValue') ? document.getElementById('rsiValue').textContent : 'غير موجود',
                macdValue: document.getElementById('macdValue') ? document.getElementById('macdValue').textContent : 'غير موجود',
                emaValue: document.getElementById('emaValue') ? document.getElementById('emaValue').textContent : 'غير موجود',
                bbValue: document.getElementById('bbValue') ? document.getElementById('bbValue').textContent : 'غير موجود'
            });
        `);
        
        console.log('📊 نتائج اختبار المؤشرات:', indicatorsTest);
        
        // طباعة التقرير النهائي
        console.log('\n' + '='.repeat(60));
        console.log('📋 تقرير اختبار الإصلاحات');
        console.log('='.repeat(60));
        
        console.log('\n💰 السعر المباشر:');
        console.log(`  عرض السعر: ${livePriceTest.priceDisplayed ? '✅' : '❌'}`);
        console.log(`  عرض التغيير: ${livePriceTest.changeDisplayed ? '✅' : '❌'}`);
        console.log(`  عرض الوقت: ${livePriceTest.timeDisplayed ? '✅' : '❌'}`);
        console.log(`  قيمة السعر: ${livePriceTest.priceValue}`);
        console.log(`  قيمة التغيير: ${livePriceTest.changeValue}`);
        
        console.log('\n📈 التحليل والرسم البياني:');
        console.log(`  النتائج مرئية: ${analysisTest.resultsVisible ? '✅' : '❌'}`);
        console.log(`  الرسم البياني موجود: ${analysisTest.chartExists ? '✅' : '❌'}`);
        console.log(`  قسم الرسم البياني مرئي: ${analysisTest.chartSectionVisible ? '✅' : '❌'}`);
        console.log(`  قسم المؤشرات مرئي: ${analysisTest.indicatorsSectionVisible ? '✅' : '❌'}`);
        console.log(`  Canvas يعمل: ${analysisTest.chartCanvas ? '✅' : '❌'}`);
        
        console.log('\n📊 المؤشرات الفنية:');
        console.log(`  RSI: ${indicatorsTest.rsiValue}`);
        console.log(`  MACD: ${indicatorsTest.macdValue}`);
        console.log(`  EMA: ${indicatorsTest.emaValue}`);
        console.log(`  Bollinger Bands: ${indicatorsTest.bbValue}`);
        
        // تحديد النتيجة الإجمالية
        const livePriceWorks = livePriceTest.priceDisplayed && livePriceTest.changeDisplayed;
        const analysisWorks = analysisTest.resultsVisible && analysisTest.chartExists;
        
        console.log('\n🏆 النتيجة النهائية:');
        if (livePriceWorks && analysisWorks) {
            console.log('✅ جميع الإصلاحات تعمل بشكل صحيح!');
            console.log('🎉 السعر المباشر والرسم البياني يعملان');
        } else {
            console.log('⚠️ بعض الإصلاحات تحتاج مراجعة:');
            if (!livePriceWorks) console.log('  - السعر المباشر لا يعمل');
            if (!analysisWorks) console.log('  - الرسم البياني أو النتائج لا تظهر');
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار التلقائي:', error);
    }
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

console.log('✅ تم تحميل اختبار الإصلاحات');
