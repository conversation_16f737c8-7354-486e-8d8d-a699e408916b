{"version": 3, "file": "WMA.js", "sourceRoot": "", "sources": ["../../src/moving_averages/WMA.ts"], "names": [], "mappings": "AAAA,YAAY,CAAA;AACZ,OAAO,EAAE,SAAS,EAAkB,MAAM,wBAAwB,CAAC;AAEnE,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAEjD,MAAM,UAAW,SAAQ,SAAS;IAKhC,YAAa,KAAa;QACxB,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACzB,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,CAAC;YAC5B,IAAI,WAAW,GAAG,MAAM,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAC,CAAC,CAAC;YAE1C,OAAO,IAAI,EAAE,CAAC;gBACZ,EAAE,CAAA,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAO,CAAC,CAAA,CAAC;oBAC1B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAClB,CAAC;gBAAA,IAAI,CAAC,CAAC;oBACL,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,IAAI,MAAM,GAAG,CAAC,CAAC;oBACf,GAAG,CAAA,CAAC,IAAI,CAAC,GAAC,CAAC,EAAE,CAAC,IAAE,MAAM,EAAE,CAAC,EAAE,EAAC,CAAC;wBAC3B,MAAM,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,GAAC,CAAC,WAAW,CAAC,CAAC,CAAA;oBACnD,CAAC;oBACD,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC;oBACxB,IAAI,CAAC,KAAK,EAAE,CAAC;oBACb,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClB,CAAC;YACH,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACjC,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtC,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAA,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAIC,oCAAoC;IACtC,SAAS,CAAC,KAAY;QAClB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,EAAE,CAAA,CAAC,MAAM,IAAI,SAAS,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAAA,CAAC;;AAPK,aAAS,GAAG,GAAG,CAAC;AASxB,CAAC;AAEF,MAAM,cAAc,KAAa;IAC3B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC"}